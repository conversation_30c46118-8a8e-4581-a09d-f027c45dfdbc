# FinPin - Smart Expense Tracking App

FinPin is a modern iOS expense tracking application that combines intelligent image recognition, cloud-based AI parsing, and seamless data management to provide a comprehensive expense tracking solution.

## 🌟 Features

### Core Features
- 📱 **Native iOS Application** - Modern SwiftUI interface optimized for iOS 16+
- 📷 **Image Recognition** - OCR-powered receipt scanning with automatic data extraction
- 🤖 **AI-Powered Parsing** - Cloud-based intelligent text parsing using OpenAI GPT
- 🗺️ **Location Tracking** - Geographic visualization of expenses with interactive maps
- 🏷️ **Smart Tagging** - Customizable tag system for expense categorization
- 💳 **Payment Methods** - Support for multiple payment cards and methods
- 📊 **Statistics & Analytics** - Comprehensive expense analysis and reporting
- 📤 **Data Export/Import** - CSV file export and import functionality
- 🎙️ **Shortcuts Integration** - Siri Shortcuts support for voice-activated expense entry
- 🔧 **Developer Tools** - Built-in debugging and testing utilities

### Technical Features
- ☁️ **Serverless Architecture** - Cloudflare Workers backend
- 🔐 **Secure Authentication** - Device-based authentication with signature verification
- 🛡️ **Security Features** - Rate limiting, anti-replay protection, encrypted communications
- 🌍 **Global CDN** - Fast worldwide access through Cloudflare network
- 📈 **Real-time Monitoring** - Comprehensive logging and performance tracking

## 🏗️ Architecture

```
┌─────────────────┐    HTTPS/TLS    ┌──────────────────┐    API Call    ┌─────────────────┐
│   iOS Client    │ ──────────────► │ Cloudflare       │ ─────────────► │   OpenAI API    │
│   (FinPin App)  │                 │ Workers/Pages    │                │   (GPT-4/Vision) │
└─────────────────┘                 └──────────────────┘                └─────────────────┘
        │                                    │
        │ Shortcuts Integration              │
        ▼                                    ▼
┌─────────────────┐                 ┌──────────────────┐
│ Device Features │                 │ Security Layer   │
│ - Local OCR     │                 │ - Rate Limiting  │
│ - CSV Export    │                 │ - Signature Auth │
│ - Data Storage  │                 │ - Request Valid  │
│ - Siri Support │                 │ - Device Auth    │
└─────────────────┘                 └──────────────────┘
```

### Data Flow
1. **Image Capture** → Local OCR → Text Extraction
2. **Text Processing** → Cloud AI Parsing → Structured Data
3. **Data Storage** → Local Database → Statistics & Analytics
4. **Export/Import** → CSV Files → Data Portability
5. **Voice Input** → Siri Shortcuts → Quick Entry

## 🚀 Quick Start

### Prerequisites

- **Development**: Xcode 15+, iOS 16+, macOS 13+
- **Server (Optional)**: Cloudflare account, OpenAI API Key, Node.js 18+

### 1. Clone and Build

```bash
# Clone project
git clone https://github.com/your-repo/FinPin.git
cd FinPin

# Open in Xcode
open FinPin.xcodeproj

# Or build via command line
xcodebuild -project FinPin.xcodeproj -scheme FinPin -destination 'platform=iOS Simulator,name=iPhone 16 Pro' build
```

### 2. Configure Server (Optional)

```bash
# Deploy server for AI parsing
cd serverless-api
npm install

# Configure secrets
export OPENAI_API_KEY=sk-your-key-here
./scripts/setup-secrets.sh production

# Deploy
./scripts/deploy.sh production
```

### 3. App Configuration

```swift
// Update server URL in ContentView.swift if using custom server
private let serverURL = "https://your-api-url.workers.dev"
```

### 4. Setup Shortcuts Integration

1. Open FinPin app → Settings → Shortcuts Integration → Setup
2. Create new shortcut in Shortcuts app
3. Add "Get Text from Input" action
4. Add "Open URL" action with: `finpin://add-expense?text=[Text]`
5. Save and add to Siri for voice activation

## 📱 App Features

### Main Interface
- **Expense List**: View recent expenses with search and filtering
- **Quick Add**: Multiple input methods (manual, camera, photo library)
- **Statistics**: Comprehensive analytics and spending insights
- **Settings**: App configuration and data management

### Data Management
- **Local Storage**: Secure local data persistence
- **CSV Export**: Export all expense data to CSV files
- **CSV Import**: Import expense data from CSV files
- **Data Validation**: Automatic data format validation and error handling

### Smart Recognition
- **OCR Processing**: Extract text from receipt images
- **AI Parsing**: Intelligent parsing of payment text using OpenAI
- **Fallback Processing**: Local parsing when server is unavailable
- **Multi-language Support**: English, Chinese, and other languages

### Shortcuts Integration
- **URL Scheme**: `finpin://add-expense?text=[expense_text]`
- **Siri Support**: Voice-activated expense entry
- **Quick Entry**: Fast expense logging via shortcuts
- **Text Processing**: Automatic parsing of spoken or typed expense data

## 🔧 Development

### Project Structure
```
FinPin/
├── FinPin/
│   ├── FinPinApp.swift          # App entry point with URL handling
│   ├── Views/
│   │   └── ContentView.swift    # Main UI implementation
│   └── Info.plist               # App configuration with URL schemes
├── serverless-api/              # Optional cloud backend
└── README.md                    # This file
```

### Building and Testing

```bash
# Build for simulator
xcodebuild -project FinPin.xcodeproj -scheme FinPin -destination 'platform=iOS Simulator,name=iPhone 16 Pro' build

# Run tests
xcodebuild test -project FinPin.xcodeproj -scheme FinPin -destination 'platform=iOS Simulator,name=iPhone 16 Pro'
```

## 🛡️ Security & Privacy

### Data Security
- **Local Storage**: All data stored locally on device
- **Secure Communication**: HTTPS/TLS encryption for server communication
- **Device Authentication**: Unique device identification for server requests
- **Request Signing**: HMAC-SHA256 signature verification
- **Rate Limiting**: Protection against abuse and excessive requests
- **No Data Collection**: No personal data stored on servers

### Privacy Features
- **Offline Capable**: Core functionality works without internet
- **Local Processing**: OCR and basic parsing performed on-device
- **Optional Cloud**: Server integration is optional for enhanced AI parsing
- **Data Control**: Full control over data export and deletion

## 🌍 Supported Features

### Languages
- **English** (Primary interface language)
- **Multi-language OCR**: Supports text recognition in multiple languages
- **Currency Detection**: Automatic currency symbol recognition

### Currencies
- USD (US Dollar) 🇺🇸
- GBP (British Pound) 🇬🇧
- EUR (Euro) 🇪🇺
- CNY (Chinese Yuan) 🇨🇳
- JPY (Japanese Yen) 🇯🇵
- And more through automatic detection

### Payment Methods
- **Digital Wallets**: Apple Pay, Google Pay, PayPal
- **Bank Cards**: Visa, Mastercard, American Express
- **UK Banks**: Monzo, Starling, Revolut, Chase, HSBC, Barclays, Santander, Lloyds, NatWest, TSB
- **Mobile Payments**: Alipay, WeChat Pay
- **Cash**: Manual cash transaction entry

### File Formats
- **CSV Export**: Standard comma-separated values format
- **CSV Import**: Support for importing expense data from CSV files
- **Image Formats**: JPEG, PNG for receipt scanning
- **Text Processing**: Plain text expense descriptions

## 📊 Usage Examples

### Manual Entry
```
Amount: $25.00
Merchant: Starbucks Coffee
Location: Downtown Seattle
Tags: Coffee, Food
Payment: Apple Pay (Chase Card)
```

### Voice Entry via Shortcuts
```
"Hey Siri, add expense"
→ "I spent 25 dollars at Starbucks for coffee"
→ Automatically parsed and added to FinPin
```

### Receipt Scanning
1. Take photo of receipt
2. OCR extracts text automatically
3. AI parsing identifies amount, merchant, date
4. Review and save expense

### CSV Data Format
```csv
Date,Merchant,Amount,Currency,Location,Tags,Payment Method,Payment Card,Notes
2024-01-15 14:30:00,Starbucks Coffee,25.00,USD,Seattle WA,Coffee;Food,Apple Pay,Chase,Morning coffee
2024-01-15 12:15:00,McDonald's,12.50,USD,Seattle WA,Food,Cash,,Quick lunch
```

## 🔍 Troubleshooting

### Common Issues

1. **OCR Recognition Problems**
   - Ensure good lighting when taking photos
   - Keep receipt flat and in focus
   - Try different angles if text isn't recognized

2. **Server Connection Issues**
   - Check internet connection
   - Verify server URL configuration
   - App works offline with local parsing

3. **Shortcuts Not Working**
   - Verify URL scheme is properly configured
   - Check Shortcuts app permissions
   - Ensure FinPin app is installed and accessible

4. **CSV Import/Export Issues**
   - Check file format matches expected CSV structure
   - Ensure proper date format (yyyy-MM-dd HH:mm:ss)
   - Verify file permissions and storage access

### Debug Tools
- Built-in debug panel in Settings
- Server health check functionality
- CSV validation and testing tools
- Sample data generation for testing

## 🚀 Roadmap

### Completed Features ✅
- Native iOS app with SwiftUI interface
- Image OCR and text recognition
- AI-powered expense parsing
- Local data storage and management
- CSV export and import functionality
- Siri Shortcuts integration
- Statistics and analytics
- Multi-currency support
- Debug and testing tools

### Future Enhancements 🔮
- iCloud sync across devices
- Apple Watch companion app
- Advanced analytics and insights
- Receipt photo storage
- Expense categories and budgets
- Multi-language interface
- Export to other formats (PDF, Excel)
- Integration with banking APIs

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

### Development Guidelines
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Standards
- **Swift**: Follow Swift API Design Guidelines
- **SwiftUI**: Use modern SwiftUI patterns and best practices
- **Comments**: Document complex logic and public APIs
- **Testing**: Add tests for new functionality

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Apple** - iOS platform, Vision framework, and SwiftUI
- **OpenAI** - GPT-4 API for intelligent text parsing
- **Cloudflare** - Workers platform for serverless backend
- **Community** - Open source contributors and testers

## 📞 Support & Contact

- **Issues**: [GitHub Issues](https://github.com/your-repo/FinPin/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-repo/FinPin/discussions)
- **Documentation**: Check the code comments and this README

---

**FinPin** - Smart expense tracking made simple 📱💰