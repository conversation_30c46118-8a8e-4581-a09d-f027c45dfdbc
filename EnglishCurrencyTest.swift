import Foundation

// Test English currency search functionality
struct EnglishCurrencyTest {
    
    static func testEnglishCurrencyNames() {
        print("🧪 Testing English currency names...")
        
        let testCurrencies = [
            "USD": "US Dollar",
            "EUR": "Euro", 
            "GBP": "British Pound",
            "JPY": "Japanese Yen",
            "CNY": "Chinese Yuan",
            "THB": "Thai Baht",
            "INR": "Indian Rupee",
            "BRL": "Brazilian Real",
            "RUB": "Russian Ruble",
            "BTC": "Bitcoin",
            "XAU": "Gold"
        ]
        
        // Mock CurrencyHelper.name function
        func mockCurrencyName(for currency: String) -> String {
            let currencyNames: [String: String] = [
                "USD": "US Dollar", "EUR": "Euro", "GBP": "British Pound", "JPY": "Japanese Yen", "CNY": "Chinese Yuan",
                "THB": "Thai Baht", "INR": "Indian Rupee", "BRL": "Brazilian Real", "RUB": "Russian Ruble",
                "BTC": "Bitcoin", "XAU": "Gold"
            ]
            return currencyNames[currency] ?? currency
        }
        
        print("\n📝 Testing currency name mappings:")
        for (code, expectedName) in testCurrencies {
            let actualName = mockCurrencyName(for: code)
            let status = actualName == expectedName ? "✅" : "❌"
            print("   \(status) \(code) -> \(actualName)")
        }
        
        // Test search functionality
        let allCurrencies = [
            "AED", "AFN", "ALL", "AMD", "ANG", "AOA", "ARS", "AUD", "AWG", "AZN",
            "BAM", "BBD", "BDT", "BGN", "BHD", "BIF", "BMD", "BND", "BOB", "BRL",
            "BSD", "BTC", "BTN", "BWP", "BYN", "BZD", "CAD", "CDF", "CHF", "CLF",
            "CLP", "CNH", "CNY", "COP", "CRC", "CUC", "CUP", "CVE", "CZK", "DJF",
            "DKK", "DOP", "DZD", "EGP", "ERN", "ETB", "EUR", "FJD", "FKP", "GBP",
            "GEL", "GGP", "GHS", "GIP", "GMD", "GNF", "GTQ", "GYD", "HKD", "HNL",
            "HRK", "HTG", "HUF", "IDR", "ILS", "IMP", "INR", "IQD", "IRR", "ISK",
            "JEP", "JMD", "JOD", "JPY", "KES", "KGS", "KHR", "KMF", "KPW", "KRW",
            "KWD", "KYD", "KZT", "LAK", "LBP", "LKR", "LRD", "LSL", "LYD", "MAD",
            "MDL", "MGA", "MKD", "MMK", "MNT", "MOP", "MRU", "MUR", "MVR", "MWK",
            "MXN", "MYR", "MZN", "NAD", "NGN", "NIO", "NOK", "NPR", "NZD", "OMR",
            "PAB", "PEN", "PGK", "PHP", "PKR", "PLN", "PYG", "QAR", "RON", "RSD",
            "RUB", "RWF", "SAR", "SBD", "SCR", "SDG", "SEK", "SGD", "SHP", "SLE",
            "SLL", "SOS", "SRD", "SSP", "STD", "STN", "SVC", "SYP", "SZL", "THB",
            "TJS", "TMT", "TND", "TOP", "TRY", "TTD", "TWD", "TZS", "UAH", "UGX",
            "USD", "UYU", "UZS", "VES", "VND", "VUV", "WST", "XAF", "XAG", "XAU",
            "XCD", "XCG", "XDR", "XOF", "XPD", "XPF", "XPT", "YER", "ZAR", "ZMW",
            "ZWG", "ZWL"
        ]
        
        let majorCurrencies = ["USD", "EUR", "GBP", "JPY", "CNY", "CAD", "AUD", "CHF", "KRW", "SGD"]
        
        func searchCurrencies(searchText: String) -> [String] {
            if searchText.isEmpty { return [] }
            
            let searchUpper = searchText.uppercased()
            return allCurrencies.filter { currency in
                !majorCurrencies.contains(currency) &&
                (currency.uppercased().contains(searchUpper) ||
                 mockCurrencyName(for: currency).uppercased().contains(searchUpper))
            }.sorted()
        }
        
        // Test English search cases
        let englishTestCases = [
            ("THB", "Search Thai Baht by code"),
            ("Thai", "Search Thai Baht by name"),
            ("BTC", "Search Bitcoin"),
            ("Indian", "Search Indian Rupee"),
            ("RU", "Search currencies containing RU"),
            ("Gold", "Search Gold"),
            ("Dollar", "Search currencies with Dollar in name")
        ]
        
        print("\n📝 Testing English search functionality:")
        for (searchTerm, description) in englishTestCases {
            let results = searchCurrencies(searchText: searchTerm)
            print("   \(description) (\(searchTerm)): \(results.count) results")
            if !results.isEmpty {
                print("      First 3: \(Array(results.prefix(3)))")
            }
        }
        
        print("\n📊 Summary:")
        print("   Total supported currencies: \(allCurrencies.count)")
        print("   Major currencies: \(majorCurrencies.count)")
        print("   Searchable currencies: \(allCurrencies.count - Set(majorCurrencies).intersection(Set(allCurrencies)).count)")
        
        print("\n✅ English currency functionality test completed!")
    }
}

// Run the test
EnglishCurrencyTest.testEnglishCurrencyNames()