# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

FinPin is a comprehensive iOS expense tracking application that combines intelligent image recognition, cloud-based AI parsing, and seamless data management. The project consists of multiple components:

1. **iOS App (FinPin/)** - Native SwiftUI application for iOS 16+
2. **Serverless API (serverless-api/ & finpin-serverless-api/)** - Cloudflare Workers backend with OpenAI integration
3. **Exchange Rate Service (cloudflare-workers/)** - Currency exchange rate caching service
4. **Website (finpin-website/)** - Static marketing website with legal pages

## Development Commands

### iOS App Development
```bash
# Build for simulator
xcodebuild -project FinPin.xcodeproj -scheme FinPin -destination 'platform=iOS Simulator,name=iPhone 16 Pro' build

# Run tests
xcodebuild test -project FinPin.xcodeproj -scheme FinPin -destination 'platform=iOS Simulator,name=iPhone 16 Pro'

# Open in Xcode
open FinPin.xcodeproj
```

### Serverless API Development
```bash
# Development server
cd serverless-api
npm run dev

# Deploy to production
npm run deploy

# Type checking
npm run type-check

# Run tests
npm run test

# Setup environment
npm run setup
```

### Exchange Rate Service
```bash
# Deploy exchange rate worker
cd cloudflare-workers
wrangler deploy

# Development testing
wrangler dev
```

## Architecture Overview

### iOS App Structure
- **FinPinApp.swift**: Main app entry point with URL scheme handling (`finpin://`)
- **Views/ContentView.swift**: Tab-based navigation with Home, Log, Stats, and Settings
- **Services/APIClient.swift**: Handles secure API communication with device authentication
- **Services/LLMParsingService.swift**: Manages OCR text extraction and AI parsing
- **Models**: `ExpenseRecord` struct with comprehensive expense data including multi-currency support
- **Intents/**: Siri Shortcuts integration for voice-activated expense entry

### Key iOS Components
- **Data Management**: Local storage with CSV import/export functionality
- **Image Processing**: Vision framework for OCR text extraction
- **Security**: Device-based authentication with HMAC-SHA256 signature verification
- **Multi-currency**: Built-in exchange rate support with currency conversion
- **Location Services**: Geographic expense tracking with MapKit integration

### Serverless API Architecture
- **Framework**: Hono.js on Cloudflare Workers
- **Authentication**: Device registration with signature-based request verification
- **AI Integration**: OpenAI GPT-4 for intelligent expense parsing
- **Security**: Rate limiting, anti-replay protection, encrypted communications
- **Endpoints**: Device registration, expense parsing, device management, statistics

### Security Implementation
- **Device Authentication**: Unique device ID generation using SHA256 hashing
- **Request Signing**: HMAC-SHA256 signatures for all API requests
- **Keychain Storage**: Secure local storage of sensitive data
- **Rate Limiting**: Protection against abuse and excessive requests
- **Environment Variables**: Secure API key management

## Data Models

### ExpenseRecord Structure
```swift
struct ExpenseRecord {
    let id: UUID
    var amount: Decimal
    var currency: String
    var merchant: String?
    var date: Date
    var transactionDate: Date?
    var latitude: Double
    var longitude: Double
    var locationName: String?
    var tags: [String]
    var paymentMethod: String?
    var paymentCard: String?
    var notes: String?
    var rawText: String?
    var confidence: DataConfidence
    var exchangeRate: Double?
    var baseCurrency: String?
    var convertedAmount: Decimal?
    var majorCurrencyAmounts: [String: Decimal]?
    var exchangeRatesSnapshot: [String: Double]?
}
```

### API Communication
- **Base URL**: `https://api.finpin.app` (configurable)
- **Authentication**: Device ID + Timestamp + HMAC Signature
- **Request Format**: JSON with structured expense data
- **Response Format**: Standardized success/error responses

## Development Guidelines

### iOS Development
- Use SwiftUI for all UI components
- Follow Swift API Design Guidelines
- Implement proper error handling with `LocalizedError`
- Use async/await for asynchronous operations
- Implement proper memory management for image processing
- Support both landscape and portrait orientations

### API Development
- Use TypeScript with strict type checking
- Implement proper error handling and logging
- Follow REST API design principles
- Use Zod for request/response validation
- Implement proper security middleware

### Testing
- iOS: Use XCTest framework for unit tests
- API: Use Vitest for serverless function testing
- Test both success and error scenarios
- Mock external dependencies (OpenAI, Cloudflare)

## Configuration

### Environment Variables
- `OPENAI_API_KEY`: OpenAI API key for expense parsing
- `ENVIRONMENT`: Development/Production environment
- `API_VERSION`: Current API version
- Custom API endpoints configurable via app settings

### URL Schemes
- `finpin://add-expense?text=[expense_text]`: Add expense from external apps
- `finpin://settings`: Open app settings
- `finpin://export`: Export expense data

## Common Issues

### Build Issues
- Ensure Xcode 15+ and iOS 16+ SDK are installed
- Check that all required capabilities are enabled in project settings
- Verify that bundle identifier matches provisioning profile

### API Issues
- Verify OpenAI API key is properly configured
- Check device registration status
- Ensure custom endpoints use HTTPS
- Monitor rate limits and request quotas

### Localization
- Primary language: English with Chinese support
- Multi-currency support with automatic detection
- Date formatting based on device locale
- Number formatting with proper currency symbols

## File Organization

```
WalletPin/
├── FinPin/                          # iOS application
│   ├── FinPinApp.swift             # App entry point
│   ├── Views/                       # SwiftUI views
│   ├── Services/                    # Business logic
│   ├── Intents/                     # Siri shortcuts
│   └── Utils/                       # Utility functions
├── serverless-api/                  # Cloudflare Workers API
│   ├── src/                         # TypeScript source
│   ├── scripts/                     # Deployment scripts
│   └── wrangler.toml               # Worker configuration
├── cloudflare-workers/              # Exchange rate service
│   ├── exchange-rate-worker.js      # Rate caching worker
│   └── wrangler.toml               # Worker configuration
└── finpin-website/                  # Static website
    ├── index.html                  # Landing page
    ├── legal/                       # Legal pages
    └── styles.css                   # Styling
```

## Dependencies

### iOS App
- SwiftUI (native framework)
- Vision (OCR text extraction)
- CryptoKit (security operations)
- MapKit (location services)
- PhotosUI (image picker)
- StoreKit (in-app purchases)

### Serverless API
- Hono.js (web framework)
- Zod (schema validation)
- OpenAI API (AI parsing)
- Cloudflare Workers (serverless platform)
- TypeScript (type safety)