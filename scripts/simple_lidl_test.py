#!/usr/bin/env python3
"""
Simple test script for bitCPM-1 model with Lidl receipt
"""

import json
import time
import requests
import os
from typing import Dict, Any

# Shortened Lidl receipt text
short_lidl_receipt = """Pizza Pepperoni 3.69
Big Pot Cherry 0.95
Frozen Onion Rings 0.85
Lemon Cookies 0.99
Cola Light 2L 0.47
TOTAL CARD 10.27
LiDL Liverpool
Date: 15/09/22 Time: 12:33:44
DEBIT MASTERCARD Amount €10.27
APPROVED"""

def simple_test():
    """
    Simple test with shorter timeout and smaller request
    """
    # ModelScope API configuration
    base_url = 'https://ms-fc-bapp-func-cnqyzbolja.cn-shanghai.fcapp.run/v1'
    api_key = os.getenv('BITCPM_API_KEY', 'ollama')  # Default token
    model = os.getenv('BITCPM_MODEL', 'OpenBMB/BitCPM4-0.5B-GGUF')
    
    messages = [
        {"role": "user", "content": f'Extract 5 keywords: "{short_lidl_receipt}"'}
    ]
    
    print(f"Calling bitCPM-1 model: {model}")
    
    # Prepare the request
    request_data = {
        "model": model,
        "messages": messages,
        "temperature": 0.1,
        "max_tokens": 200
    }
    
    # Make the API call and measure time
    start_time = time.time()
    try:
        response = requests.post(
            f"{base_url}/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            },
            json=request_data,
            timeout=60  # Increased timeout to 60 seconds
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"Response status: {response.status_code}")
        print(f"Response time: {response_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print("SUCCESS!")
            print(f"Server response:\n{json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # Print the actual content
            content = result["choices"][0]["message"]["content"]
            print(f"\nContent:\n{content}")
            return True
        else:
            print(f"API error: {response.status_code}")
            print(f"Response content: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        end_time = time.time()
        response_time = end_time - start_time
        print(f"API call timed out after {response_time:.2f}s")
        return False
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        print(f"Unexpected error after {response_time:.2f}s: {e}")
        return False

def test_5_words_extraction():
    """
    Test extracting exactly 5 words
    """
    # ModelScope API configuration
    base_url = 'https://ms-fc-bapp-func-cnqyzbolja.cn-shanghai.fcapp.run/v1'
    api_key = os.getenv('BITCPM_API_KEY', 'ollama')  # Default token
    model = os.getenv('BITCPM_MODEL', 'OpenBMB/BitCPM4-0.5B-GGUF')
    
    messages = [
        {"role": "user", "content": "List exactly 5 words that represent food items from this list: Pizza Pepperoni, Big Pot Cherry, Frozen Onion Rings, Lemon Cookies, Cola Light. Respond with JSON format: {\"keywords\": [\"word1\", \"word2\", \"word3\", \"word4\", \"word5\"]}"}
    ]
    
    print(f"\nTesting 5 words extraction...")
    print(f"Calling bitCPM-1 model: {model}")
    
    # Prepare the request
    request_data = {
        "model": model,
        "messages": messages,
        "temperature": 0.1,
        "max_tokens": 200
    }
    
    # Make the API call and measure time
    start_time = time.time()
    try:
        response = requests.post(
            f"{base_url}/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            },
            json=request_data,
            timeout=60  # Increased timeout to 60 seconds
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"Response status: {response.status_code}")
        print(f"Response time: {response_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print("SUCCESS!")
            print(f"Server response:\n{json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # Print the actual content
            content = result["choices"][0]["message"]["content"]
            print(f"\nContent:\n{content}")
            return True
        else:
            print(f"API error: {response.status_code}")
            print(f"Response content: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        end_time = time.time()
        response_time = end_time - start_time
        print(f"API call timed out after {response_time:.2f}s")
        return False
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        print(f"Unexpected error after {response_time:.2f}s: {e}")
        return False

if __name__ == "__main__":
    print("Simple bitCPM-1 Test with Lidl Data")
    print("=" * 40)
    
    # Test 1: Simple test
    success1 = simple_test()
    
    # Test 2: 5 words extraction
    success2 = test_5_words_extraction()
    
    print("\n" + "=" * 40)
    print("RESULTS")
    print("=" * 40)
    print(f"Simple test: {'PASS' if success1 else 'FAIL'}")
    print(f"5 words extraction: {'PASS' if success2 else 'FAIL'}")