#!/usr/bin/env python3
"""
Minimal test script for bitCPM-1 model
"""

import json
import time
import requests
import os

def minimal_test():
    """
    Minimal test with very simple input
    """
    # ModelScope API configuration
    base_url = 'https://ms-fc-bapp-func-cnqyzbolja.cn-shanghai.fcapp.run/v1'
    api_key = os.getenv('BITCPM_API_KEY', 'ollama')  # Default token
    model = os.getenv('BITCPM_MODEL', 'OpenBMB/BitCPM4-0.5B-GGUF')
    
    messages = [
        {"role": "user", "content": "What is 2+2?"}
    ]
    
    print(f"Calling bitCPM-1 model: {model}")
    print("Request: What is 2+2?")
    
    # Prepare the request
    request_data = {
        "model": model,
        "messages": messages,
        "temperature": 0.1,
        "max_tokens": 100
    }
    
    # Make the API call and measure time
    start_time = time.time()
    try:
        response = requests.post(
            f"{base_url}/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            },
            json=request_data,
            timeout=60  # Increased timeout to 60 seconds
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"Response status: {response.status_code}")
        print(f"Response time: {response_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print("SUCCESS!")
            
            # Print the actual content
            content = result["choices"][0]["message"]["content"]
            print(f"Response: {content}")
            return True
        else:
            print(f"API error: {response.status_code}")
            print(f"Response content: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        end_time = time.time()
        response_time = end_time - start_time
        print(f"API call timed out after {response_time:.2f}s")
        return False
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        print(f"Unexpected error after {response_time:.2f}s: {e}")
        return False

def test_chinese():
    """
    Test with Chinese input
    """
    # ModelScope API configuration
    base_url = 'https://ms-fc-bapp-func-cnqyzbolja.cn-shanghai.fcapp.run/v1'
    api_key = os.getenv('BITCPM_API_KEY', 'ollama')  # Default token
    model = os.getenv('BITCPM_MODEL', 'OpenBMB/BitCPM4-0.5B-GGUF')
    
    messages = [
        {"role": "user", "content": "你好"}
    ]
    
    print(f"\nTesting Chinese input...")
    print(f"Calling bitCPM-1 model: {model}")
    print("Request: 你好")
    
    # Prepare the request
    request_data = {
        "model": model,
        "messages": messages,
        "temperature": 0.1,
        "max_tokens": 100
    }
    
    # Make the API call and measure time
    start_time = time.time()
    try:
        response = requests.post(
            f"{base_url}/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            },
            json=request_data,
            timeout=60  # Increased timeout to 60 seconds
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"Response status: {response.status_code}")
        print(f"Response time: {response_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print("SUCCESS!")
            
            # Print the actual content
            content = result["choices"][0]["message"]["content"]
            print(f"Response: {content}")
            return True
        else:
            print(f"API error: {response.status_code}")
            print(f"Response content: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        end_time = time.time()
        response_time = end_time - start_time
        print(f"API call timed out after {response_time:.2f}s")
        return False
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        print(f"Unexpected error after {response_time:.2f}s: {e}")
        return False

if __name__ == "__main__":
    print("Minimal bitCPM-1 Test")
    print("=" * 20)
    
    # Test 1: Simple math
    success1 = minimal_test()
    
    # Test 2: Chinese
    success2 = test_chinese()
    
    print("\n" + "=" * 20)
    print("RESULTS")
    print("=" * 20)
    print(f"Simple math test: {'PASS' if success1 else 'FAIL'}")
    print(f"Chinese test: {'PASS' if success2 else 'FAIL'}")
    
    if not success1 and not success2:
        print("\nServer appears to be having issues (possibly out of disk space)")
        print("Recommendation: Try again later or contact the server administrator")