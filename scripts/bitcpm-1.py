#!/usr/bin/env python3
"""
Test script for bitCPM-1 model performance and accuracy
Using the same prompts as in FinPin's openai.ts
"""

import json
import time
import requests
from typing import Dict, Any

# Test cases from various transaction types
test_cases = [
    {
        "name": "Costa Coffee Transaction",
        "text": "Costa Coffee\nHounslow, England\nTransaction Time: 2023-09-20 01:47\nAmount: £9.65\nPayment Method: HSBC UK Visa Debit Card",
        "expected": {
            "amount": "9.65",
            "currency": "GBP",
            "merchant": "Costa Coffee",
            "payment_method": "Debit Card",
            "payment_card": "HSBC",
            "location": "Hounslow, England",
            "timestamp": "2023-09-20T01:47:00Z"
        }
    },
    {
        "name": "Chinese Receipt Transaction",
        "text": "星巴克\n北京\n交易时间: 2023-09-20 14:30\n金额: ¥25.00\n支付方式: 支付宝",
        "expected": {
            "amount": "25.00",
            "currency": "CNY",
            "merchant": "星巴克",
            "payment_method": "支付宝",
            "payment_card": None,
            "location": "北京",
            "timestamp": "2023-09-20T14:30:00Z"
        }
    },
    {
        "name": "Hotel Transaction",
        "text": "Aurora Star Airport Hotel\nKeflavik, Iceland\nDate: 2022/12/16 14:09\nTotal: ISK 2,799\nCard: Monzo Mastercard",
        "expected": {
            "amount": "2799",
            "currency": "ISK",
            "merchant": "Aurora Star Airport Hotel",
            "payment_method": "Credit Card",
            "payment_card": "Monzo",
            "location": "Keflavik, Iceland",
            "timestamp": "2022-12-16T14:09:00Z"
        }
    },
    {
        "name": "US Transaction",
        "text": "Starbucks\nNew York, USA\nTime: 09:41\nAmount: $12.50\nPayment: Apple Pay",
        "expected": {
            "amount": "12.50",
            "currency": "USD",
            "merchant": "Starbucks",
            "payment_method": "Apple Pay",
            "payment_card": None,
            "location": "New York, USA",
            "timestamp": None  # No date provided
        }
    }
]

# System prompt from openai.ts (simplified for testing)
system_prompt = """You are an expert financial transaction parser specializing in mobile payment receipts and bank transaction records. Extract structured information from payment text in any language with high accuracy.

CRITICAL: Respond with valid JSON only. No explanations, comments, or additional text.

EXTRACTION TARGETS:
- amount: Numerical value only (string, remove currency symbols, commas, spaces)
- currency: ISO 4217 code (USD, GBP, EUR, CNY, JPY, ISK, etc.)
- merchant: Primary business/merchant name (clean, without extra info)
- payment_method: Payment method (Apple Pay, Google Pay, Alipay, WeChat Pay, Credit Card, Debit Card, etc.)
- payment_card: Specific card/bank (Monzo, HSBC, Starling, Chase, Visa, Mastercard, etc.)
- location: Geographic location (city, country, or address)
- timestamp: ISO 8601 format (YYYY-MM-DDTHH:mm:ssZ) or null
- confidence: Accuracy score (0.0-1.0)
- extensions: Additional metadata

PARSING RULES:
1. AMOUNT: Look for numerical values with currency symbols (£, $, ¥, €, kr, ISK, etc.)
   - Remove all non-numeric characters except decimal points
   - Handle formats: "£9.65", "ISK 2,799", "¥25.00", "$12.50"

2. CURRENCY: Map symbols and codes to ISO standards
   - £ → GBP, $ → USD, ¥ → CNY/JPY, € → EUR, kr/ISK → ISK
   - Look for explicit codes: USD, GBP, EUR, CNY, JPY, ISK
   - If no explicit currency is mentioned but the text contains Chinese payment-related keywords (e.g., "财付通", "零钱", "小程序", "淘宝", "支付宝", "余额宝"), infer CNY.
   - For English/UK contexts without explicit currency, infer GBP.
   - For US contexts, infer USD.
   - When no explicit currency is detected, use comprehensive inference (in priority order):
     - PRIORITY 1: User's location context (e.g., user in UK → GBP, user in China → CNY, user in Iceland → ISK)
     - PRIORITY 2: Merchant/location information from text (e.g., "Iceland" → ISK, "Japan" → JPY, "China/中国" → CNY)
     - PRIORITY 3: Text language context (e.g., Chinese characters → CNY, Icelandic text → ISK, Japanese text → JPY)
     - PRIORITY 4: Payment method context (e.g., Alipay/支付宝 → CNY, WeChat Pay/微信支付 → CNY)
     - PRIORITY 5: Merchant brand context (e.g., known Chinese brands → CNY, known UK brands → GBP)
   - Apply inference even for ambiguous amounts without clear currency symbols
   - Default to USD only if all inference methods fail

3. MERCHANT: Extract primary business name
   - Prioritize recognizable brand names: "Costa Coffee", "Starbucks", "McDonald's"
   - Clean up: "Costa Coffee，英格兰 Hounslow" → "Costa Coffee"
   - Ignore: transaction IDs, addresses, extra descriptors

4. PAYMENT_METHOD: Identify payment method
   - Mobile: Apple Pay, Google Pay, Samsung Pay
   - Chinese: Alipay, WeChat Pay, 支付宝, 微信支付
   - Cards: Credit Card, Debit Card, Visa, Mastercard
   - If card type mentioned: "Visa Debit Card" → "Debit Card"

5. PAYMENT_CARD: Identify bank/card provider
   - Banks: Monzo, HSBC, Chase, Starling, Revolut, etc.
   - Card types: Visa, Mastercard, American Express
   - Format: "HSBC UK Visa Debit Card" → "HSBC"

6. LOCATION: Extract geographic information
   - Cities: "Hounslow", "凯夫拉维克", "Beijing"
   - Countries: "英格兰" → "England", "Iceland"
   - Airports: "机场" indicates airport location

7. TIMESTAMP: Parse actual transaction date/time from the receipt text
   - PRIORITIZE time found in the receipt text over any user provided timestamp
   - Look for explicit transaction time labels: "Transaction Time:", "Time:", "Date:", "交易时间:", "时间:", "日期:", etc.
   - Prioritize dates with clear transaction context over general dates
   - Formats: "2023-09-20 01:47", "2022/12/16 14:09", "09:41", "2023年09月20日 01:47"
   - For timestamps, first try to infer timezone from content (e.g., Chinese keywords like "支付宝", "微信" imply UTC+8). Convert to UTC by subtracting 8 hours if UTC+8. If unable to infer, parse as local time without adjustment. Return in ISO 8601 format (YYYY-MM-DDTHH:mm:ssZ).
   - Use user provided timestamp ONLY as fallback if no time is found in text, applying the same timezone adjustment
   - Set to null if no time information available at all

8. EXTENSIONS: Add contextual information
   - category: Food & Beverage, Transportation, Shopping, Accommodation, etc.
   - tags: Relevant keywords ["coffee", "airport", "hotel", "restaurant"]
   - description: Brief summary of the transaction

9. CONFIDENCE: Base on information clarity
   - 0.9-1.0: All key fields clearly identified
   - 0.7-0.9: Most fields identified, some ambiguity
   - 0.5-0.7: Basic info only, significant ambiguity
   - <0.5: Very unclear or incomplete

LANGUAGE HANDLING:
- English: Standard processing
- Chinese: Handle mixed Chinese/English text
- Other languages: Extract recognizable elements

EXAMPLE RESPONSES:

Costa Coffee Transaction:
{
  "amount": "9.65",
  "currency": "GBP",
  "merchant": "Costa Coffee",
  "payment_method": "Debit Card",
  "payment_card": "HSBC",
  "location": "Hounslow, England",
  "timestamp": "2023-09-20T01:47:00Z",
  "confidence": 0.92,
  "extensions": {
    "category": "Food & Beverage",
    "tags": ["coffee", "food", "airport"],
    "description": "Costa Coffee purchase at Hounslow"
  }
}

Chinese Receipt Transaction:
{
  "amount": "25.00",
  "currency": "CNY",
  "merchant": "星巴克",
  "payment_method": "支付宝",
  "payment_card": null,
  "location": "北京",
  "timestamp": "2023-09-20T14:30:00Z",
  "confidence": 0.95,
  "extensions": {
    "category": "Food & Beverage",
    "tags": ["coffee", "chinese"],
    "description": "星巴克消费"
  }
}

Hotel Transaction:
{
  "amount": "2799",
  "currency": "ISK",
  "merchant": "Aurora Star Airport Hotel",
  "payment_method": "Credit Card",
  "payment_card": "Monzo",
  "location": "Keflavik, Iceland",
  "timestamp": "2022-12-16T14:09:00Z",
  "confidence": 0.88,
  "extensions": {
    "category": "Accommodation",
    "tags": ["hotel", "airport", "travel"],
    "description": "Airport hotel payment in Iceland"
  }
}"""

def build_user_prompt(text: str) -> str:
    """Build user prompt with transaction text"""
    prompt = f'''TRANSACTION TEXT TO PARSE:

"{text}"

PARSING INSTRUCTIONS:
1. Carefully analyze the text for financial transaction information
2. Extract all identifiable elements according to the system rules
3. Pay special attention to currency symbols and amount formatting
4. CRITICAL: For currency inference when no explicit symbol is present, use ALL available context:
   - User location, merchant location, text language, payment method, and merchant brand
   - Do NOT default to USD without attempting comprehensive inference first
5. Identify merchant names even if mixed with location/address info
6. Handle multi-language text (English, Chinese, etc.)
7. CRITICAL: For timestamp, look specifically for transaction time labels (Time:, Date:, 交易时间:, etc.) and prioritize these over general dates
8. Return only the JSON response, no other text

PARSE NOW:'''
    return prompt

def call_bitcpm_1(messages: list) -> Dict[str, Any]:
    """
    Call bitCPM-1 model
    This is a placeholder function - replace with actual API call to bitCPM-1
    """
    # This is where you would implement the actual call to bitCPM-1
    # For now, we'll simulate a response with a delay
    time.sleep(1)  # Simulate network delay
    
    # Return a mock response for testing
    return {
        "choices": [{
            "message": {
                "content": json.dumps({
                    "amount": "9.65",
                    "currency": "GBP",
                    "merchant": "Costa Coffee",
                    "payment_method": "Debit Card",
                    "payment_card": "HSBC",
                    "location": "Hounslow, England",
                    "timestamp": "2023-09-20T01:47:00Z",
                    "confidence": 0.92,
                    "extensions": {
                        "category": "Food & Beverage",
                        "tags": ["coffee", "food", "airport"],
                        "description": "Costa Coffee purchase at Hounslow"
                    }
                })
            }
        }]
    }

def evaluate_response(response: Dict[str, Any], expected: Dict[str, Any]) -> Dict[str, Any]:
    """Evaluate the accuracy of the response compared to expected values"""
    try:
        content = response["choices"][0]["message"]["content"]
        parsed = json.loads(content)
        print(parsed)
    except (KeyError, json.JSONDecodeError) as e:
        return {
            "accuracy": 0.0,
            "correct_fields": 0,
            "total_fields": len(expected),
            "errors": [f"Failed to parse response: {str(e)}"]
        }
    
    correct_fields = 0
    total_fields = len(expected)
    errors = []
    
    for field, expected_value in expected.items():
        if field in parsed:
            actual_value = parsed[field]
            if actual_value == expected_value:
                correct_fields += 1
            else:
                errors.append(f"{field}: expected '{expected_value}', got '{actual_value}'")
        else:
            errors.append(f"Missing field: {field}")
    
    accuracy = correct_fields / total_fields if total_fields > 0 else 0
    
    return {
        "accuracy": accuracy,
        "correct_fields": correct_fields,
        "total_fields": total_fields,
        "errors": errors
    }

def main():
    """Main test function"""
    print("Testing bitCPM-1 model performance and accuracy")
    print("=" * 50)
    
    results = []
    total_time = 0
    
    for i, test_case in enumerate(test_cases):
        print(f"\nTest {i+1}/{len(test_cases)}: {test_case['name']}")
        print("-" * 30)
        
        # Build prompts
        user_prompt = build_user_prompt(test_case["text"])
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        # Measure response time
        start_time = time.time()
        response = call_bitcpm_1(messages)
        end_time = time.time()
        
        response_time = end_time - start_time
        total_time += response_time
        
        # Evaluate response
        evaluation = evaluate_response(response, test_case["expected"])
        
        # Store results
        result = {
            "test_name": test_case["name"],
            "response_time": response_time,
            "accuracy": evaluation["accuracy"],
            "correct_fields": evaluation["correct_fields"],
            "total_fields": evaluation["total_fields"],
            "errors": evaluation["errors"]
        }
        results.append(result)
        
        # Print results
        print(f"Response time: {response_time:.2f}s")
        print(f"Accuracy: {evaluation['accuracy']:.2%}")
        print(f"Fields: {evaluation['correct_fields']}/{evaluation['total_fields']}")
        if evaluation["errors"]:
            print("Errors:")
            for error in evaluation["errors"]:
                print(f"  - {error}")
    
    # Print summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    avg_response_time = total_time / len(test_cases)
    avg_accuracy = sum(r["accuracy"] for r in results) / len(results)
    
    print(f"Average response time: {avg_response_time:.2f}s")
    print(f"Average accuracy: {avg_accuracy:.2%}")
    print(f"Total tests: {len(test_cases)}")
    
    print("\nDetailed Results:")
    for result in results:
        print(f"  {result['test_name']}: {result['accuracy']:.2%} accuracy, {result['response_time']:.2f}s")

if __name__ == "__main__":
    main()