#!/usr/bin/env python3
"""
Test script for bitCPM-1 model with Lidl receipt
Testing speed and printing server response
"""

import json
import time
import requests
import os
from typing import Dict, Any

# Lidl receipt text
lidl_receipt = """Pizza Pepperoni
Froz ChunkyCodBatter
3.69
Big Pot Cherry
0.95
Frozen Onion Rings
0.85
Lemon Cookies
0.99
Cola Light 2L
Simply Still Water
0.47 B
0.23
Pain au Chocolat
0.55 A
Jam Filled Doughnut.
0.35 A
TOTAL
CARD
10.27
10.27
LiDL
Liverpool
VAT NO. GB350396892
*CUSTOMER COPY*
- PLEASE RETAIN RECEIPT
Date: 15/09/22
Time: 12:33:44
MID: ***91192
TID: ****0406
TRNS NO: ********************
DEBIT MASTERCARD
************9369
A0000000041010
Contactless
SALE
Amount €10.27
Verification Not Required
APPROVED
AUTH CODE 100833
PLEASE DEBIT ACCOUNT WITH TOTAL SHOWN
VAT RATE
0 *
B
20 %
SALES &
9.57
0.70
VAT £
0.00
0.12
Download the Lidl Plus app to save on your next shop
0308
543162/81
15.09.22
Enter survey: lidl.co.uk/haveyoursay
& you can win £100 of Lidl Vouchers.
12:30"""

def call_bitcpm_1_lidl_test() -> Dict[str, Any]:
    """
    Call bitCPM-1 model via ModelScope API with Lidl receipt
    """
    # ModelScope API configuration
    base_url = 'https://ms-fc-bapp-func-cnqyzbolja.cn-shanghai.fcapp.run/v1'
    api_key = os.getenv('BITCPM_API_KEY', 'ollama')  # Default token
    model = os.getenv('BITCPM_MODEL', 'OpenBMB/BitCPM4-0.5B-GGUF')
    
    messages = [
        {"role": "system", "content": "You are an expert financial transaction parser. Extract structured information from payment text. Respond with valid JSON only."},
        {"role": "user", "content": f'Parse this receipt and extract the items and total amount: "{lidl_receipt}"'}
    ]
    
    print(f"Calling bitCPM-1 model: {model}")
    print(f"Input text length: {len(lidl_receipt)} characters")
    
    # Prepare the request
    request_data = {
        "model": model,
        "messages": messages,
        "temperature": 0.1,
        "max_tokens": 1000
    }
    
    # Make the API call and measure time
    start_time = time.time()
    try:
        response = requests.post(
            f"{base_url}/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            },
            json=request_data,
            timeout=30
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"Response status: {response.status_code}")
        print(f"Response time: {response_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print("API call successful")
            return {
                "success": True,
                "response_time": response_time,
                "response": result,
                "error": None
            }
        else:
            print(f"API error: {response.status_code}")
            print(f"Response content: {response.text}")
            return {
                "success": False,
                "response_time": response_time,
                "response": None,
                "error": f"HTTP {response.status_code}: {response.text}"
            }
            
    except requests.exceptions.Timeout:
        end_time = time.time()
        response_time = end_time - start_time
        print("API call timed out")
        return {
            "success": False,
            "response_time": response_time,
            "response": None,
            "error": "Timeout"
        }
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        print(f"Unexpected error: {e}")
        return {
            "success": False,
            "response_time": response_time,
            "response": None,
            "error": str(e)
        }

def extract_5_words_test():
    """
    Test extracting 5 key words from the receipt
    """
    # ModelScope API configuration
    base_url = 'https://ms-fc-bapp-func-cnqyzbolja.cn-shanghai.fcapp.run/v1'
    api_key = os.getenv('BITCPM_API_KEY', 'ollama')  # Default token
    model = os.getenv('BITCPM_MODEL', 'OpenBMB/BitCPM4-0.5B-GGUF')
    
    messages = [
        {"role": "system", "content": "Extract exactly 5 key words from this receipt text that best represent what was purchased. Respond with valid JSON only containing a 'keywords' array."},
        {"role": "user", "content": f'Receipt: "{lidl_receipt}"'}
    ]
    
    print(f"\nExtracting 5 key words...")
    print(f"Calling bitCPM-1 model: {model}")
    
    # Prepare the request
    request_data = {
        "model": model,
        "messages": messages,
        "temperature": 0.1,
        "max_tokens": 200
    }
    
    # Make the API call and measure time
    start_time = time.time()
    try:
        response = requests.post(
            f"{base_url}/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            },
            json=request_data,
            timeout=30
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"Response status: {response.status_code}")
        print(f"Response time: {response_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print("API call successful")
            return {
                "success": True,
                "response_time": response_time,
                "response": result,
                "error": None
            }
        else:
            print(f"API error: {response.status_code}")
            print(f"Response content: {response.text}")
            return {
                "success": False,
                "response_time": response_time,
                "response": None,
                "error": f"HTTP {response.status_code}: {response.text}"
            }
            
    except requests.exceptions.Timeout:
        end_time = time.time()
        response_time = end_time - start_time
        print("API call timed out")
        return {
            "success": False,
            "response_time": response_time,
            "response": None,
            "error": "Timeout"
        }
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        print(f"Unexpected error: {e}")
        return {
            "success": False,
            "response_time": response_time,
            "response": None,
            "error": str(e)
        }

def main():
    """Main test function"""
    print("Testing bitCPM-1 Model with Lidl Receipt")
    print("=" * 50)
    
    # Test 1: Parse the full receipt
    print("\nTest 1: Parsing Full Receipt")
    print("-" * 30)
    result1 = call_bitcpm_1_lidl_test()
    
    if result1["success"]:
        try:
            content = result1["response"]["choices"][0]["message"]["content"]
            print(f"\nServer Response:\n{content}")
            
            # Try to parse as JSON
            parsed = json.loads(content)
            print(f"\nParsed JSON:\n{json.dumps(parsed, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"Could not parse response as JSON: {e}")
            print(f"Raw response: {content}")
    else:
        print(f"Test failed: {result1['error']}")
    
    # Test 2: Extract 5 key words
    print("\n\nTest 2: Extracting 5 Key Words")
    print("-" * 30)
    result2 = extract_5_words_test()
    
    if result2["success"]:
        try:
            content = result2["response"]["choices"][0]["message"]["content"]
            print(f"\nServer Response:\n{content}")
            
            # Try to parse as JSON
            parsed = json.loads(content)
            print(f"\nParsed JSON:\n{json.dumps(parsed, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"Could not parse response as JSON: {e}")
            print(f"Raw response: {content}")
    else:
        print(f"Test failed: {result2['error']}")
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    print(f"Full receipt parsing: {'✓' if result1['success'] else '✗'} ({result1['response_time']:.2f}s)")
    print(f"5 words extraction: {'✓' if result2['success'] else '✗'} ({result2['response_time']:.2f}s)")

if __name__ == "__main__":
    main()