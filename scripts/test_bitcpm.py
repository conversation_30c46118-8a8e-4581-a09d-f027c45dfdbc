#!/usr/bin/env python3
"""
Simple test script for bitCPM-1 model connection
"""

import os
import requests
import json

def test_bitcpm_connection():
    """Test connection to bitCPM-1 model"""
    # ModelScope API configuration
    base_url = 'https://ms-fc-d59b6756-af0d.api-inference.modelscope.cn/v1'
    api_key = os.getenv('BITCPM_API_KEY', 'ms-319e9625-0f80-4aed-b0ec-5e1524be96b0')  # Default token
    model = os.getenv('BITCPM_MODEL', 'OpenBMB/BitCPM4-0.5B-GGUF')
    
    print(f"Testing connection to {model}")
    
    # Simple test message
    messages = [
        {
            'role': 'system',
            'content': 'You are a helpful assistant.'
        },
        {
            'role': 'user',
            'content': '你好'
        }
    ]
    
    # Prepare the request
    request_data = {
        "model": model,
        "messages": messages,
        "stream": False  # Disable streaming for simple test
    }
    
    print(f"Request data: {request_data}")
    
    # Make the API call
    try:
        response = requests.post(
            f"{base_url}/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            },
            json=request_data,
            timeout=60  # 60 second timeout
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {response.headers}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success! Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"Error: {response.status_code}")
            print(f"Response content: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("Request timed out")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"Connection error: {e}")
        return False
    except requests.exceptions.RequestException as e:
        print(f"Request error: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False

if __name__ == "__main__":
    test_bitcpm_connection()