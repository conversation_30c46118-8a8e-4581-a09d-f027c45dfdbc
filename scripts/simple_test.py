#!/usr/bin/env python3
"""
Simple test script for bitCPM-1 model with different transaction types
"""

import os
import requests
import json

def test_transaction_parsing():
    """Test parsing different transaction types with bitCPM-1"""
    # ModelScope API configuration
    base_url = 'https://ms-fc-d59b6756-af0d.api-inference.modelscope.cn/v1'
    api_key = os.getenv('BITCPM_API_KEY', 'ms-319e9625-0f80-4aed-b0ec-5e1524be96b0')  # Default token
    model = os.getenv('BITCPM_MODEL', 'OpenBMB/BitCPM4-0.5B-GGUF')
    
    # Test cases
    test_cases = [
        {
            "name": "Costa Coffee Transaction",
            "text": "Costa Coffee\\nHounslow, England\\nTransaction Time: 2023-09-20 01:47\\nAmount: £9.65\\nPayment Method: HSBC UK Visa Debit Card"
        },
        {
            "name": "Chinese Receipt Transaction", 
            "text": "星巴克\\n北京\\n交易时间: 2023-09-20 14:30\\n金额: ¥25.00\\n支付方式: 支付宝"
        },
        {
            "name": "Hotel Transaction",
            "text": "Aurora Star Airport Hotel\\nKeflavik, Iceland\\nDate: 2022/12/16 14:09\\nTotal: ISK 2,799\\nCard: Monzo Mastercard"
        },
        {
            "name": "US Transaction",
            "text": "Starbucks\\nNew York, USA\\nTime: 09:41\\nAmount: $12.50\\nPayment: Apple Pay"
        }
    ]
    
    system_prompt = "You are an expert financial transaction parser. Extract structured information from payment text. Respond with valid JSON only."

    for i, test_case in enumerate(test_cases):
        print(f"\nTest {i+1}/{len(test_cases)}: {test_case['name']}")
        print("-" * 30)
        print(f"Input text: {test_case['text']}")
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f'Parse this transaction: "{test_case["text"]}"'}
        ]
        
        # Prepare the request
        request_data = {
            "model": model,
            "messages": messages,
            "temperature": 0.1,
            "max_tokens": 500
        }
        
        # Make the API call
        try:
            response = requests.post(
                f"{base_url}/chat/completions",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                },
                json=request_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                print(f"Response: {content}")
            else:
                print(f"Error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"Request failed: {e}")

if __name__ == "__main__":
    test_transaction_parsing()