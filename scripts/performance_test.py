#!/usr/bin/env python3
"""
Performance test script for bitCPM-1 model
Testing speed and accuracy with the new environment
"""

import json
import time
import requests
import os
from typing import Dict, Any

def call_bitcpm_1_simple(text: str) -> Dict[str, Any]:
    """
    Call bitCPM-1 model via ModelScope API (new environment)
    Simplified version for performance testing
    """
    # New ModelScope API configuration
    base_url = 'https://ms-fc-bapp-func-cnqyzbolja.cn-shanghai.fcapp.run/v1'
    api_key = os.getenv('BITCPM_API_KEY', 'ollama')  # Default token
    model = os.getenv('BITCPM_MODEL', 'OpenBMB/BitCPM4-0.5B-GGUF')
    
    messages = [
        {"role": "system", "content": "You are an expert financial transaction parser. Extract structured information from payment text. Respond with valid JSON only."},
        {"role": "user", "content": f'Parse this transaction: "{text}"'}
    ]
    
    # Prepare the request
    request_data = {
        "model": model,
        "messages": messages,
        "temperature": 0.1,
        "max_tokens": 500
    }
    
    # Make the API call and measure time
    start_time = time.time()
    try:
        response = requests.post(
            f"{base_url}/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            },
            json=request_data,
            timeout=30
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            return {
                "success": True,
                "response_time": response_time,
                "response": result,
                "error": None
            }
        else:
            return {
                "success": False,
                "response_time": response_time,
                "response": None,
                "error": f"HTTP {response.status_code}: {response.text}"
            }
            
    except requests.exceptions.Timeout:
        end_time = time.time()
        return {
            "success": False,
            "response_time": end_time - start_time,
            "response": None,
            "error": "Timeout"
        }
    except Exception as e:
        end_time = time.time()
        return {
            "success": False,
            "response_time": end_time - start_time,
            "response": None,
            "error": str(e)
        }

def performance_test():
    """Run performance tests"""
    print("Performance Testing for bitCPM-1 Model")
    print("=" * 50)
    
    # Test cases with different complexity
    test_cases = [
        {"name": "Simple English", "text": "Coffee $5.50"},
        {"name": "Medium English", "text": "Starbucks, New York. Amount: $12.50. Card: Apple Pay"},
        {"name": "Complex English", "text": "Costa Coffee\nHounslow, England\nTransaction Time: 2023-09-20 01:47\nAmount: £9.65\nPayment Method: HSBC UK Visa Debit Card"},
        {"name": "Chinese", "text": "星巴克\n北京\n交易时间: 2023-09-20 14:30\n金额: ¥25.00\n支付方式: 支付宝"},
        {"name": "Mixed", "text": "Aurora Star Airport Hotel\nKeflavik, Iceland\nDate: 2022/12/16 14:09\nTotal: ISK 2,799\nCard: Monzo Mastercard"}
    ]
    
    results = []
    
    # Run tests
    for i, test_case in enumerate(test_cases):
        print(f"\nTest {i+1}/{len(test_cases)}: {test_case['name']}")
        print("-" * 30)
        
        result = call_bitcpm_1_simple(test_case["text"])
        results.append({
            "test_name": test_case["name"],
            "input": test_case["text"],
            "result": result
        })
        
        if result["success"]:
            print(f"✓ Success - Response time: {result['response_time']:.2f}s")
            # Try to extract some info from response
            try:
                content = result["response"]["choices"][0]["message"]["content"]
                print(f"Response preview: {content[:100]}...")
            except:
                print("Response preview: <unable to parse>")
        else:
            print(f"✗ Failed - Response time: {result['response_time']:.2f}s")
            print(f"Error: {result['error']}")
    
    # Summary
    print("\n" + "=" * 50)
    print("PERFORMANCE SUMMARY")
    print("=" * 50)
    
    successful_tests = [r for r in results if r["result"]["success"]]
    failed_tests = [r for r in results if not r["result"]["success"]]
    
    print(f"Successful tests: {len(successful_tests)}/{len(test_cases)}")
    print(f"Failed tests: {len(failed_tests)}/{len(test_cases)}")
    
    if successful_tests:
        avg_response_time = sum(r["result"]["response_time"] for r in successful_tests) / len(successful_tests)
        min_response_time = min(r["result"]["response_time"] for r in successful_tests)
        max_response_time = max(r["result"]["response_time"] for r in successful_tests)
        
        print(f"\nResponse Times (successful tests):")
        print(f"  Average: {avg_response_time:.2f}s")
        print(f"  Minimum: {min_response_time:.2f}s")
        print(f"  Maximum: {max_response_time:.2f}s")
    
    # Detailed results
    print("\nDetailed Results:")
    for result in results:
        status = "✓" if result["result"]["success"] else "✗"
        time_str = f"{result['result']['response_time']:.2f}s"
        print(f"  {status} {result['test_name']}: {time_str}")

def price_estimate_test():
    """Estimate pricing based on usage"""
    print("\n" + "=" * 50)
    print("PRICE ESTIMATION")
    print("=" * 50)
    
    # Simulate usage patterns
    usage_patterns = {
        "Light User": {"requests_per_day": 5, "tokens_per_request": 300},
        "Medium User": {"requests_per_day": 20, "tokens_per_request": 400},
        "Heavy User": {"requests_per_day": 50, "tokens_per_request": 500}
    }
    
    # Assumed pricing (these would need to be verified with the actual provider)
    estimated_price_per_million_tokens = 0.10  # $0.10 per million tokens
    
    print("Assumed pricing: $0.10 per million tokens")
    print("(Actual pricing should be verified with the provider)")
    
    for user_type, pattern in usage_patterns.items():
        daily_tokens = pattern["requests_per_day"] * pattern["tokens_per_request"]
        monthly_tokens = daily_tokens * 30
        
        daily_cost = (daily_tokens / 1_000_000) * estimated_price_per_million_tokens
        monthly_cost = (monthly_tokens / 1_000_000) * estimated_price_per_million_tokens
        
        print(f"\n{user_type}:")
        print(f"  Requests per day: {pattern['requests_per_day']}")
        print(f"  Tokens per request: {pattern['tokens_per_request']}")
        print(f"  Daily tokens: {daily_tokens:,}")
        print(f"  Monthly tokens: {monthly_tokens:,}")
        print(f"  Estimated daily cost: ${daily_cost:.4f}")
        print(f"  Estimated monthly cost: ${monthly_cost:.2f}")

if __name__ == "__main__":
    performance_test()
    price_estimate_test()