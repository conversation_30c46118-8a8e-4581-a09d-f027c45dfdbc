#!/usr/bin/env python3

import plistlib
import os

# 读取项目文件
project_file = "FinPin.xcodeproj/project.pbxproj"

# 由于project.pbxproj不是标准的plist格式，我们需要用文本方式处理
with open(project_file, 'r') as f:
    content = f.read()

# 检查是否已经包含intentdefinition引用
if "AddExpenseIntent.intentdefinition" in content:
    print("项目文件中已包含intentdefinition引用")
    exit(0)

# 生成新的唯一ID
import uuid
base_id = str(uuid.uuid4()).replace('-', '').upper()[:16]

file_ref_id = f"D1{base_id[:14]}001C7F48"
build_file_id = f"D1{base_id[:14]}001C7F49"

print(f"File Reference ID: {file_ref_id}")
print(f"Build File ID: {build_file_id}")

# 找到合适的位置插入FileReference
# 在PrivacyPolicy.md之后插入
privacy_policy_line = None
lines = content.split('\n')
for i, line in enumerate(lines):
    if 'A1000037000000000000001 /* PrivacyPolicy.md */' in line:
        privacy_policy_line = i
        break

if privacy_policy_line is None:
    print("未找到PrivacyPolicy.md的引用，无法插入FileReference")
    exit(1)

# 插入FileReference
file_ref_entry = f'\t\t{file_ref_id} /* AddExpenseIntent.intentdefinition */ = {{isa = PBXFileReference; lastKnownFileType = file.intentdefinition; path = AddExpenseIntent.intentdefinition; sourceTree = "<group>"; }};'
lines.insert(privacy_policy_line + 1, file_ref_entry)

# 找到合适的位置插入BuildFile
# 在premium-1.storekit之后插入
premium_storekit_line = None
for i, line in enumerate(lines):
    if 'D1968DD52E3A448A001DF072 /* premium-1.storekit in Resources */' in line:
        premium_storekit_line = i
        break

if premium_storekit_line is None:
    print("未找到premium-1.storekit的引用，无法插入BuildFile")
    exit(1)

# 插入BuildFile
build_file_entry = f'\t\t{build_file_id} /* AddExpenseIntent.intentdefinition in Resources */ = {{isa = PBXBuildFile; fileRef = {file_ref_id} /* AddExpenseIntent.intentdefinition */; }};'
lines.insert(premium_storekit_line + 1, build_file_entry)

# 找到Resources build phase中合适的位置插入BuildFile引用
# 在Preview Assets.xcassets之后插入
preview_assets_line = None
for i, line in enumerate(lines):
    if 'A1000007000000000000001 /* Preview Assets.xcassets in Resources */' in line:
        preview_assets_line = i
        break

if preview_assets_line is None:
    print("未找到Preview Assets.xcassets的引用，无法插入BuildFile引用")
    exit(1)

# 找到Resources build phase的结束位置
resources_end_line = None
for i in range(preview_assets_line, len(lines)):
    if ');' in lines[i] and 'runOnlyForDeploymentPostprocessing' in lines[i+1] if i+1 < len(lines) else False:
        resources_end_line = i
        break

if resources_end_line is None:
    print("未找到Resources build phase的结束位置")
    exit(1)

# 插入BuildFile引用（在结束括号之前）
build_file_ref_entry = f'\t\t\t\t{build_file_id} /* AddExpenseIntent.intentdefinition in Resources */,'
lines.insert(resources_end_line, build_file_ref_entry)

# 写回文件
with open(project_file, 'w') as f:
    f.write('\n'.join(lines))

print("项目文件修改完成")