<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>archiveVersion</key>
	<string>1</string>
	<key>classes</key>
	<dict/>
	<key>objectVersion</key>
	<string>70</string>
	<key>objects</key>
	<dict>
		<key>A1000001000000000000001</key>
		<dict>
			<key>fileRef</key>
			<string>A1000002000000000000001</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>A1000002000000000000001</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>sourcecode.swift</string>
			<key>path</key>
			<string>FinPinApp.swift</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>A1000003000000000000001</key>
		<dict>
			<key>fileRef</key>
			<string>A1000004000000000000001</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>A1000004000000000000001</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>sourcecode.swift</string>
			<key>path</key>
			<string>ContentView.swift</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>A1000005000000000000001</key>
		<dict>
			<key>fileRef</key>
			<string>A1000006000000000000001</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>A1000006000000000000001</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>folder.assetcatalog</string>
			<key>path</key>
			<string>Assets.xcassets</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>A1000007000000000000001</key>
		<dict>
			<key>fileRef</key>
			<string>A1000008000000000000001</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>A1000008000000000000001</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>folder.assetcatalog</string>
			<key>path</key>
			<string>Preview Assets.xcassets</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>A1000009000000000000001</key>
		<dict>
			<key>fileRef</key>
			<string>A1000010000000000000001</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>A1000010000000000000001</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>sourcecode.swift</string>
			<key>path</key>
			<string>LegalDocumentView.swift</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>A1000011000000000000001</key>
		<dict>
			<key>explicitFileType</key>
			<string>wrapper.application</string>
			<key>includeInIndex</key>
			<string>0</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>path</key>
			<string>FinPin.app</string>
			<key>sourceTree</key>
			<string>BUILT_PRODUCTS_DIR</string>
		</dict>
		<key>A1000012000000000000001</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>text.plist.xml</string>
			<key>path</key>
			<string>Info.plist</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>A1000013000000000000001</key>
		<dict>
			<key>buildActionMask</key>
			<string>**********</string>
			<key>files</key>
			<array/>
			<key>isa</key>
			<string>PBXFrameworksBuildPhase</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>A1000014000000000000001</key>
		<dict>
			<key>children</key>
			<array>
				<string>D1968DD42E3A448A001DF072</string>
				<string>A1000015000000000000001</string>
				<string>D130281E2E4EA4F2001C7F46</string>
				<string>D13028292E4EA4F3001C7F46</string>
				<string>D130281B2E4EA4F2001C7F46</string>
				<string>A1000016000000000000001</string>
				<string>D13027F32E4EA37D001C7F46</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>A1000015000000000000001</key>
		<dict>
			<key>children</key>
			<array>
				<string>D18B7D04CD7EF943B9001C7F46</string>
				<string>A1000018000000000000001</string>
				<string>A1000035000000000000001</string>
				<string>A1000037000000000000001</string>
				<string>A1000006000000000000001</string>
				<string>A1000012000000000000001</string>
				<string>A1000033000000000000001</string>
				<string>A1000022000000000000001</string>
				<string>D102D0AD2E3CEFFE00260DB5</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>path</key>
			<string>FinPin</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>A1000016000000000000001</key>
		<dict>
			<key>children</key>
			<array>
				<string>A1000011000000000000001</string>
				<string>D130281A2E4EA4F2001C7F46</string>
				<string>D13028262E4EA4F3001C7F46</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>name</key>
			<string>Products</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>A1000018000000000000001</key>
		<dict>
			<key>children</key>
			<array>
				<string>A1000004000000000000001</string>
				<string>A1000010000000000000001</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>path</key>
			<string>Views</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>A1000022000000000000001</key>
		<dict>
			<key>children</key>
			<array>
				<string>A1000008000000000000001</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>path</key>
			<string>Preview Content</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>A1000023000000000000001</key>
		<dict>
			<key>buildConfigurationList</key>
			<string>A1000024000000000000001</string>
			<key>buildPhases</key>
			<array>
				<string>A1000025000000000000001</string>
				<string>A1000013000000000000001</string>
				<string>A1000026000000000000001</string>
				<string>D13028362E4EA4F3001C7F46</string>
			</array>
			<key>buildRules</key>
			<array/>
			<key>dependencies</key>
			<array>
				<string>D13028312E4EA4F3001C7F46</string>
				<string>D13028342E4EA4F3001C7F46</string>
			</array>
			<key>fileSystemSynchronizedGroups</key>
			<array>
				<string>D102D0AD2E3CEFFE00260DB5</string>
			</array>
			<key>isa</key>
			<string>PBXNativeTarget</string>
			<key>name</key>
			<string>FinPin</string>
			<key>productName</key>
			<string>FinPin</string>
			<key>productReference</key>
			<string>A1000011000000000000001</string>
			<key>productType</key>
			<string>com.apple.product-type.application</string>
		</dict>
		<key>A1000024000000000000001</key>
		<dict>
			<key>buildConfigurations</key>
			<array>
				<string>A1000031000000000000001</string>
				<string>A1000032000000000000001</string>
			</array>
			<key>defaultConfigurationIsVisible</key>
			<string>0</string>
			<key>defaultConfigurationName</key>
			<string>Release</string>
			<key>isa</key>
			<string>XCConfigurationList</string>
		</dict>
		<key>A1000025000000000000001</key>
		<dict>
			<key>buildActionMask</key>
			<string>**********</string>
			<key>files</key>
			<array>
				<string>A1000003000000000000001</string>
				<string>A1000009000000000000001</string>
				<string>A1000001000000000000001</string>
				<string>D1968DD62E3A448A001DF073</string>
			</array>
			<key>isa</key>
			<string>PBXSourcesBuildPhase</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>A1000026000000000000001</key>
		<dict>
			<key>buildActionMask</key>
			<string>**********</string>
			<key>files</key>
			<array>
				<string>A1000007000000000000001</string>
				<string>D18B7D04CD7EF943B7001C7F46</string>
				<string>D1968DD52E3A448A001DF072</string>
				<string>A1000034000000000000001</string>
				<string>A1000036000000000000001</string>
				<string>A1000005000000000000001</string>
			</array>
			<key>isa</key>
			<string>PBXResourcesBuildPhase</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>A1000027000000000000001</key>
		<dict>
			<key>attributes</key>
			<dict>
				<key>BuildIndependentTargetsInParallel</key>
				<string>1</string>
				<key>LastSwiftUpdateCheck</key>
				<string>1640</string>
				<key>LastUpgradeCheck</key>
				<string>1640</string>
				<key>TargetAttributes</key>
				<dict>
					<key>A1000023000000000000001</key>
					<dict>
						<key>CreatedOnToolsVersion</key>
						<string>15.4</string>
					</dict>
					<key>D13028192E4EA4F2001C7F46</key>
					<dict>
						<key>CreatedOnToolsVersion</key>
						<string>16.4</string>
					</dict>
					<key>D13028252E4EA4F3001C7F46</key>
					<dict>
						<key>CreatedOnToolsVersion</key>
						<string>16.4</string>
					</dict>
				</dict>
			</dict>
			<key>buildConfigurationList</key>
			<string>A1000028000000000000001</string>
			<key>compatibilityVersion</key>
			<string>Xcode 14.0</string>
			<key>developmentRegion</key>
			<string>en</string>
			<key>hasScannedForEncodings</key>
			<string>0</string>
			<key>isa</key>
			<string>PBXProject</string>
			<key>knownRegions</key>
			<array>
				<string>en</string>
				<string>zh-Hans</string>
				<string>Base</string>
			</array>
			<key>mainGroup</key>
			<string>A1000014000000000000001</string>
			<key>productRefGroup</key>
			<string>A1000016000000000000001</string>
			<key>projectDirPath</key>
			<string></string>
			<key>projectRoot</key>
			<string></string>
			<key>targets</key>
			<array>
				<string>A1000023000000000000001</string>
				<string>D13028192E4EA4F2001C7F46</string>
				<string>D13028252E4EA4F3001C7F46</string>
			</array>
		</dict>
		<key>A1000028000000000000001</key>
		<dict>
			<key>buildConfigurations</key>
			<array>
				<string>A1000029000000000000001</string>
				<string>A1000030000000000000001</string>
			</array>
			<key>defaultConfigurationIsVisible</key>
			<string>0</string>
			<key>defaultConfigurationName</key>
			<string>Release</string>
			<key>isa</key>
			<string>XCConfigurationList</string>
		</dict>
		<key>A1000029000000000000001</key>
		<dict>
			<key>buildSettings</key>
			<dict>
				<key>ALWAYS_SEARCH_USER_PATHS</key>
				<string>NO</string>
				<key>ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS</key>
				<string>YES</string>
				<key>CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED</key>
				<string>YES</string>
				<key>CLANG_ANALYZER_NONNULL</key>
				<string>YES</string>
				<key>CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION</key>
				<string>YES_AGGRESSIVE</string>
				<key>CLANG_CXX_LANGUAGE_STANDARD</key>
				<string>gnu++20</string>
				<key>CLANG_ENABLE_MODULES</key>
				<string>YES</string>
				<key>CLANG_ENABLE_OBJC_ARC</key>
				<string>YES</string>
				<key>CLANG_ENABLE_OBJC_WEAK</key>
				<string>YES</string>
				<key>CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING</key>
				<string>YES</string>
				<key>CLANG_WARN_BOOL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_COMMA</key>
				<string>YES</string>
				<key>CLANG_WARN_CONSTANT_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS</key>
				<string>YES</string>
				<key>CLANG_WARN_DIRECT_OBJC_ISA_USAGE</key>
				<string>YES_ERROR</string>
				<key>CLANG_WARN_DOCUMENTATION_COMMENTS</key>
				<string>YES</string>
				<key>CLANG_WARN_EMPTY_BODY</key>
				<string>YES</string>
				<key>CLANG_WARN_ENUM_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_INFINITE_RECURSION</key>
				<string>YES</string>
				<key>CLANG_WARN_INT_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_NON_LITERAL_NULL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_LITERAL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_ROOT_CLASS</key>
				<string>YES_ERROR</string>
				<key>CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER</key>
				<string>YES</string>
				<key>CLANG_WARN_RANGE_LOOP_ANALYSIS</key>
				<string>YES</string>
				<key>CLANG_WARN_STRICT_PROTOTYPES</key>
				<string>YES</string>
				<key>CLANG_WARN_SUSPICIOUS_MOVE</key>
				<string>YES</string>
				<key>CLANG_WARN_UNGUARDED_AVAILABILITY</key>
				<string>YES_AGGRESSIVE</string>
				<key>CLANG_WARN_UNREACHABLE_CODE</key>
				<string>YES</string>
				<key>CLANG_WARN__DUPLICATE_METHOD_MATCH</key>
				<string>YES</string>
				<key>COPY_PHASE_STRIP</key>
				<string>NO</string>
				<key>DEBUG_INFORMATION_FORMAT</key>
				<string>dwarf</string>
				<key>DEVELOPMENT_TEAM</key>
				<string>C5ZV969PR3</string>
				<key>ENABLE_STRICT_OBJC_MSGSEND</key>
				<string>YES</string>
				<key>ENABLE_TESTABILITY</key>
				<string>YES</string>
				<key>ENABLE_USER_SCRIPT_SANDBOXING</key>
				<string>YES</string>
				<key>GCC_C_LANGUAGE_STANDARD</key>
				<string>gnu17</string>
				<key>GCC_DYNAMIC_NO_PIC</key>
				<string>NO</string>
				<key>GCC_NO_COMMON_BLOCKS</key>
				<string>YES</string>
				<key>GCC_OPTIMIZATION_LEVEL</key>
				<string>0</string>
				<key>GCC_PREPROCESSOR_DEFINITIONS</key>
				<array>
					<string>DEBUG=1</string>
					<string>$(inherited)</string>
				</array>
				<key>GCC_WARN_64_TO_32_BIT_CONVERSION</key>
				<string>YES</string>
				<key>GCC_WARN_ABOUT_RETURN_TYPE</key>
				<string>YES_ERROR</string>
				<key>GCC_WARN_UNDECLARED_SELECTOR</key>
				<string>YES</string>
				<key>GCC_WARN_UNINITIALIZED_AUTOS</key>
				<string>YES_AGGRESSIVE</string>
				<key>GCC_WARN_UNUSED_FUNCTION</key>
				<string>YES</string>
				<key>GCC_WARN_UNUSED_VARIABLE</key>
				<string>YES</string>
				<key>IPHONEOS_DEPLOYMENT_TARGET</key>
				<string>16.0</string>
				<key>LOCALIZATION_PREFERS_STRING_CATALOGS</key>
				<string>YES</string>
				<key>MTL_ENABLE_DEBUG_INFO</key>
				<string>INCLUDE_SOURCE</string>
				<key>MTL_FAST_MATH</key>
				<string>YES</string>
				<key>ONLY_ACTIVE_ARCH</key>
				<string>YES</string>
				<key>SDKROOT</key>
				<string>iphoneos</string>
				<key>SWIFT_ACTIVE_COMPILATION_CONDITIONS</key>
				<string>DEBUG $(inherited)</string>
				<key>SWIFT_OPTIMIZATION_LEVEL</key>
				<string>-Onone</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Debug</string>
		</dict>
		<key>A1000030000000000000001</key>
		<dict>
			<key>buildSettings</key>
			<dict>
				<key>ALWAYS_SEARCH_USER_PATHS</key>
				<string>NO</string>
				<key>ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS</key>
				<string>YES</string>
				<key>CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED</key>
				<string>YES</string>
				<key>CLANG_ANALYZER_NONNULL</key>
				<string>YES</string>
				<key>CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION</key>
				<string>YES_AGGRESSIVE</string>
				<key>CLANG_CXX_LANGUAGE_STANDARD</key>
				<string>gnu++20</string>
				<key>CLANG_ENABLE_MODULES</key>
				<string>YES</string>
				<key>CLANG_ENABLE_OBJC_ARC</key>
				<string>YES</string>
				<key>CLANG_ENABLE_OBJC_WEAK</key>
				<string>YES</string>
				<key>CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING</key>
				<string>YES</string>
				<key>CLANG_WARN_BOOL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_COMMA</key>
				<string>YES</string>
				<key>CLANG_WARN_CONSTANT_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS</key>
				<string>YES</string>
				<key>CLANG_WARN_DIRECT_OBJC_ISA_USAGE</key>
				<string>YES_ERROR</string>
				<key>CLANG_WARN_DOCUMENTATION_COMMENTS</key>
				<string>YES</string>
				<key>CLANG_WARN_EMPTY_BODY</key>
				<string>YES</string>
				<key>CLANG_WARN_ENUM_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_INFINITE_RECURSION</key>
				<string>YES</string>
				<key>CLANG_WARN_INT_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_NON_LITERAL_NULL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_LITERAL_CONVERSION</key>
				<string>YES</string>
				<key>CLANG_WARN_OBJC_ROOT_CLASS</key>
				<string>YES_ERROR</string>
				<key>CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER</key>
				<string>YES</string>
				<key>CLANG_WARN_RANGE_LOOP_ANALYSIS</key>
				<string>YES</string>
				<key>CLANG_WARN_STRICT_PROTOTYPES</key>
				<string>YES</string>
				<key>CLANG_WARN_SUSPICIOUS_MOVE</key>
				<string>YES</string>
				<key>CLANG_WARN_UNGUARDED_AVAILABILITY</key>
				<string>YES_AGGRESSIVE</string>
				<key>CLANG_WARN_UNREACHABLE_CODE</key>
				<string>YES</string>
				<key>CLANG_WARN__DUPLICATE_METHOD_MATCH</key>
				<string>YES</string>
				<key>COPY_PHASE_STRIP</key>
				<string>NO</string>
				<key>DEBUG_INFORMATION_FORMAT</key>
				<string>dwarf-with-dsym</string>
				<key>DEVELOPMENT_TEAM</key>
				<string>C5ZV969PR3</string>
				<key>ENABLE_NS_ASSERTIONS</key>
				<string>NO</string>
				<key>ENABLE_STRICT_OBJC_MSGSEND</key>
				<string>YES</string>
				<key>ENABLE_USER_SCRIPT_SANDBOXING</key>
				<string>YES</string>
				<key>GCC_C_LANGUAGE_STANDARD</key>
				<string>gnu17</string>
				<key>GCC_NO_COMMON_BLOCKS</key>
				<string>YES</string>
				<key>GCC_WARN_64_TO_32_BIT_CONVERSION</key>
				<string>YES</string>
				<key>GCC_WARN_ABOUT_RETURN_TYPE</key>
				<string>YES_ERROR</string>
				<key>GCC_WARN_UNDECLARED_SELECTOR</key>
				<string>YES</string>
				<key>GCC_WARN_UNINITIALIZED_AUTOS</key>
				<string>YES_AGGRESSIVE</string>
				<key>GCC_WARN_UNUSED_FUNCTION</key>
				<string>YES</string>
				<key>GCC_WARN_UNUSED_VARIABLE</key>
				<string>YES</string>
				<key>IPHONEOS_DEPLOYMENT_TARGET</key>
				<string>16.0</string>
				<key>LOCALIZATION_PREFERS_STRING_CATALOGS</key>
				<string>YES</string>
				<key>MTL_ENABLE_DEBUG_INFO</key>
				<string>NO</string>
				<key>MTL_FAST_MATH</key>
				<string>YES</string>
				<key>SDKROOT</key>
				<string>iphoneos</string>
				<key>SWIFT_COMPILATION_MODE</key>
				<string>wholemodule</string>
				<key>VALIDATE_PRODUCT</key>
				<string>YES</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Release</string>
		</dict>
		<key>A1000031000000000000001</key>
		<dict>
			<key>buildSettings</key>
			<dict>
				<key>ASSETCATALOG_COMPILER_APPICON_NAME</key>
				<string>AppIcon</string>
				<key>ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME</key>
				<string>AccentColor</string>
				<key>CODE_SIGN_ENTITLEMENTS</key>
				<string>FinPin/FinPin.entitlements</string>
				<key>CODE_SIGN_IDENTITY</key>
				<string>Apple Development</string>
				<key>CODE_SIGN_STYLE</key>
				<string>Automatic</string>
				<key>CURRENT_PROJECT_VERSION</key>
				<string>8</string>
				<key>DEVELOPMENT_ASSET_PATHS</key>
				<string>"FinPin/Preview Content"</string>
				<key>DEVELOPMENT_TEAM</key>
				<string>F8KSY9YZVT</string>
				<key>ENABLE_PREVIEWS</key>
				<string>YES</string>
				<key>GENERATE_INFOPLIST_FILE</key>
				<string>YES</string>
				<key>INFOPLIST_FILE</key>
				<string>FinPin/Info.plist</string>
				<key>INFOPLIST_KEY_CFBundleDisplayName</key>
				<string>FinPin</string>
				<key>INFOPLIST_KEY_ITSAppUsesNonExemptEncryption</key>
				<string>NO</string>
				<key>INFOPLIST_KEY_LSApplicationCategoryType</key>
				<string>public.app-category.finance</string>
				<key>INFOPLIST_KEY_NSCameraUsageDescription</key>
				<string>FinPin uses your camera to capture payment receipts and transaction screenshots for expense tracking. This helps you maintain accurate financial records by automatically extracting payment information from images.</string>
				<key>INFOPLIST_KEY_NSLocationWhenInUseUsageDescription</key>
				<string>FinPin uses your location to automatically tag expenses with the location where they occurred. This helps you track spending patterns by location and provides context for your financial records. Location data is stored locally on your device.</string>
				<key>INFOPLIST_KEY_NSPhotoLibraryUsageDescription</key>
				<string>FinPin needs access to your photo library to select existing payment receipts and transaction screenshots for expense tracking. This allows you to import financial documents you've already captured.</string>
				<key>INFOPLIST_KEY_NSSiriUsageDescription</key>
				<string>FinPin uses Siri to allow you to quickly add expenses using voice commands. You can say "Hey Siri, add expense to FinPin" to record transactions hands-free. All voice processing is handled by Apple and no audio data is stored by FinPin.</string>
				<key>INFOPLIST_KEY_NSSupportsLiveActivities</key>
				<string>NO</string>
				<key>INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents</key>
				<string>YES</string>
				<key>INFOPLIST_KEY_UIRequiredDeviceCapabilities</key>
				<string>armv7</string>
				<key>INFOPLIST_KEY_UISupportedInterfaceOrientations</key>
				<string>UIInterfaceOrientationPortrait</string>
				<key>INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad</key>
				<string>UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight</string>
				<key>LD_RUNPATH_SEARCH_PATHS</key>
				<array>
					<string>$(inherited)</string>
					<string>@executable_path/Frameworks</string>
				</array>
				<key>MARKETING_VERSION</key>
				<string>1.0.2</string>
				<key>PRODUCT_BUNDLE_IDENTIFIER</key>
				<string>com.finpin.app</string>
				<key>PRODUCT_NAME</key>
				<string>$(TARGET_NAME)</string>
				<key>PROVISIONING_PROFILE_SPECIFIER</key>
				<string></string>
				<key>SUPPORTED_PLATFORMS</key>
				<string>iphoneos iphonesimulator</string>
				<key>SUPPORTS_MACCATALYST</key>
				<string>NO</string>
				<key>SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD</key>
				<string>NO</string>
				<key>SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD</key>
				<string>NO</string>
				<key>SWIFT_EMIT_LOC_STRINGS</key>
				<string>YES</string>
				<key>SWIFT_VERSION</key>
				<string>5.0</string>
				<key>TARGETED_DEVICE_FAMILY</key>
				<string>1</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Debug</string>
		</dict>
		<key>A1000032000000000000001</key>
		<dict>
			<key>buildSettings</key>
			<dict>
				<key>ASSETCATALOG_COMPILER_APPICON_NAME</key>
				<string>AppIcon</string>
				<key>ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME</key>
				<string>AccentColor</string>
				<key>CODE_SIGN_ENTITLEMENTS</key>
				<string>FinPin/FinPin.entitlements</string>
				<key>CODE_SIGN_STYLE</key>
				<string>Automatic</string>
				<key>CURRENT_PROJECT_VERSION</key>
				<string>8</string>
				<key>DEVELOPMENT_ASSET_PATHS</key>
				<string>"FinPin/Preview Content"</string>
				<key>DEVELOPMENT_TEAM</key>
				<string>F8KSY9YZVT</string>
				<key>ENABLE_PREVIEWS</key>
				<string>YES</string>
				<key>GENERATE_INFOPLIST_FILE</key>
				<string>YES</string>
				<key>INFOPLIST_FILE</key>
				<string>FinPin/Info.plist</string>
				<key>INFOPLIST_KEY_CFBundleDisplayName</key>
				<string>FinPin</string>
				<key>INFOPLIST_KEY_ITSAppUsesNonExemptEncryption</key>
				<string>NO</string>
				<key>INFOPLIST_KEY_LSApplicationCategoryType</key>
				<string>public.app-category.finance</string>
				<key>INFOPLIST_KEY_NSCameraUsageDescription</key>
				<string>FinPin uses your camera to capture payment receipts and transaction screenshots for expense tracking. This helps you maintain accurate financial records by automatically extracting payment information from images.</string>
				<key>INFOPLIST_KEY_NSLocationWhenInUseUsageDescription</key>
				<string>FinPin uses your location to automatically tag expenses with the location where they occurred. This helps you track spending patterns by location and provides context for your financial records. Location data is stored locally on your device.</string>
				<key>INFOPLIST_KEY_NSPhotoLibraryUsageDescription</key>
				<string>FinPin needs access to your photo library to select existing payment receipts and transaction screenshots for expense tracking. This allows you to import financial documents you've already captured.</string>
				<key>INFOPLIST_KEY_NSSiriUsageDescription</key>
				<string>FinPin uses Siri to allow you to quickly add expenses using voice commands. You can say "Hey Siri, add expense to FinPin" to record transactions hands-free. All voice processing is handled by Apple and no audio data is stored by FinPin.</string>
				<key>INFOPLIST_KEY_NSSupportsLiveActivities</key>
				<string>NO</string>
				<key>INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents</key>
				<string>YES</string>
				<key>INFOPLIST_KEY_UIRequiredDeviceCapabilities</key>
				<string>armv7</string>
				<key>INFOPLIST_KEY_UISupportedInterfaceOrientations</key>
				<string>UIInterfaceOrientationPortrait</string>
				<key>INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad</key>
				<string>UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight</string>
				<key>LD_RUNPATH_SEARCH_PATHS</key>
				<array>
					<string>$(inherited)</string>
					<string>@executable_path/Frameworks</string>
				</array>
				<key>MARKETING_VERSION</key>
				<string>1.0.2</string>
				<key>PRODUCT_BUNDLE_IDENTIFIER</key>
				<string>com.finpin.app</string>
				<key>PRODUCT_NAME</key>
				<string>$(TARGET_NAME)</string>
				<key>SUPPORTED_PLATFORMS</key>
				<string>iphoneos iphonesimulator</string>
				<key>SUPPORTS_MACCATALYST</key>
				<string>NO</string>
				<key>SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD</key>
				<string>NO</string>
				<key>SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD</key>
				<string>NO</string>
				<key>SWIFT_EMIT_LOC_STRINGS</key>
				<string>YES</string>
				<key>SWIFT_VERSION</key>
				<string>5.0</string>
				<key>TARGETED_DEVICE_FAMILY</key>
				<string>1</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Release</string>
		</dict>
		<key>A1000033000000000000001</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>text.plist.entitlements</string>
			<key>path</key>
			<string>FinPin.entitlements</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>A1000034000000000000001</key>
		<dict>
			<key>fileRef</key>
			<string>A1000035000000000000001</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>A1000035000000000000001</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>net.daringfireball.markdown</string>
			<key>path</key>
			<string>TermsOfService.md</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>A1000036000000000000001</key>
		<dict>
			<key>fileRef</key>
			<string>A1000037000000000000001</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>A1000037000000000000001</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>net.daringfireball.markdown</string>
			<key>path</key>
			<string>PrivacyPolicy.md</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>D102D0AD2E3CEFFE00260DB5</key>
		<dict>
			<key>explicitFileTypes</key>
			<dict/>
			<key>explicitFolders</key>
			<array/>
			<key>isa</key>
			<string>PBXFileSystemSynchronizedRootGroup</string>
			<key>path</key>
			<string>AppIcon.appiconset</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>D13027F32E4EA37D001C7F46</key>
		<dict>
			<key>children</key>
			<array>
				<string>A1000002000000000000001</string>
				<string>D1968DD72E3A448A001DF073</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>name</key>
			<string>Recovered References</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>D13028162E4EA4F2001C7F46</key>
		<dict>
			<key>buildActionMask</key>
			<string>**********</string>
			<key>files</key>
			<array/>
			<key>isa</key>
			<string>PBXSourcesBuildPhase</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>D13028172E4EA4F2001C7F46</key>
		<dict>
			<key>buildActionMask</key>
			<string>**********</string>
			<key>files</key>
			<array>
				<string>D130281D2E4EA4F2001C7F46</string>
			</array>
			<key>isa</key>
			<string>PBXFrameworksBuildPhase</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>D13028182E4EA4F2001C7F46</key>
		<dict>
			<key>buildActionMask</key>
			<string>**********</string>
			<key>files</key>
			<array/>
			<key>isa</key>
			<string>PBXResourcesBuildPhase</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>D13028192E4EA4F2001C7F46</key>
		<dict>
			<key>buildConfigurationList</key>
			<string>D130283C2E4EA4F3001C7F46</string>
			<key>buildPhases</key>
			<array>
				<string>D13028162E4EA4F2001C7F46</string>
				<string>D13028172E4EA4F2001C7F46</string>
				<string>D13028182E4EA4F2001C7F46</string>
			</array>
			<key>buildRules</key>
			<array/>
			<key>dependencies</key>
			<array/>
			<key>fileSystemSynchronizedGroups</key>
			<array>
				<string>D130281E2E4EA4F2001C7F46</string>
			</array>
			<key>isa</key>
			<string>PBXNativeTarget</string>
			<key>name</key>
			<string>FinPinIntentsExtension</string>
			<key>packageProductDependencies</key>
			<array/>
			<key>productName</key>
			<string>FinPinIntentsExtension</string>
			<key>productReference</key>
			<string>D130281A2E4EA4F2001C7F46</string>
			<key>productType</key>
			<string>com.apple.product-type.app-extension</string>
		</dict>
		<key>D130281A2E4EA4F2001C7F46</key>
		<dict>
			<key>explicitFileType</key>
			<string>wrapper.app-extension</string>
			<key>includeInIndex</key>
			<string>0</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>path</key>
			<string>FinPinIntentsExtension.appex</string>
			<key>sourceTree</key>
			<string>BUILT_PRODUCTS_DIR</string>
		</dict>
		<key>D130281B2E4EA4F2001C7F46</key>
		<dict>
			<key>children</key>
			<array>
				<string>D130281C2E4EA4F2001C7F46</string>
				<string>D13028272E4EA4F3001C7F46</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>name</key>
			<string>Frameworks</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>D130281C2E4EA4F2001C7F46</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>wrapper.framework</string>
			<key>name</key>
			<string>Intents.framework</string>
			<key>path</key>
			<string>System/Library/Frameworks/Intents.framework</string>
			<key>sourceTree</key>
			<string>SDKROOT</string>
		</dict>
		<key>D130281D2E4EA4F2001C7F46</key>
		<dict>
			<key>fileRef</key>
			<string>D130281C2E4EA4F2001C7F46</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>D130281E2E4EA4F2001C7F46</key>
		<dict>
			<key>exceptions</key>
			<array>
				<string>D130283B2E4EA4F3001C7F46</string>
			</array>
			<key>explicitFileTypes</key>
			<dict/>
			<key>explicitFolders</key>
			<array/>
			<key>isa</key>
			<string>PBXFileSystemSynchronizedRootGroup</string>
			<key>path</key>
			<string>FinPinIntentsExtension</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>D13028222E4EA4F3001C7F46</key>
		<dict>
			<key>buildActionMask</key>
			<string>**********</string>
			<key>files</key>
			<array/>
			<key>isa</key>
			<string>PBXSourcesBuildPhase</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>D13028232E4EA4F3001C7F46</key>
		<dict>
			<key>buildActionMask</key>
			<string>**********</string>
			<key>files</key>
			<array>
				<string>D13028282E4EA4F3001C7F46</string>
			</array>
			<key>isa</key>
			<string>PBXFrameworksBuildPhase</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>D13028242E4EA4F3001C7F46</key>
		<dict>
			<key>buildActionMask</key>
			<string>**********</string>
			<key>files</key>
			<array/>
			<key>isa</key>
			<string>PBXResourcesBuildPhase</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>D13028252E4EA4F3001C7F46</key>
		<dict>
			<key>buildConfigurationList</key>
			<string>D130283E2E4EA4F3001C7F46</string>
			<key>buildPhases</key>
			<array>
				<string>D13028222E4EA4F3001C7F46</string>
				<string>D13028232E4EA4F3001C7F46</string>
				<string>D13028242E4EA4F3001C7F46</string>
			</array>
			<key>buildRules</key>
			<array/>
			<key>dependencies</key>
			<array/>
			<key>fileSystemSynchronizedGroups</key>
			<array>
				<string>D13028292E4EA4F3001C7F46</string>
			</array>
			<key>isa</key>
			<string>PBXNativeTarget</string>
			<key>name</key>
			<string>FinPinIntentsExtensionUI</string>
			<key>packageProductDependencies</key>
			<array/>
			<key>productName</key>
			<string>FinPinIntentsExtensionUI</string>
			<key>productReference</key>
			<string>D13028262E4EA4F3001C7F46</string>
			<key>productType</key>
			<string>com.apple.product-type.app-extension</string>
		</dict>
		<key>D13028262E4EA4F3001C7F46</key>
		<dict>
			<key>explicitFileType</key>
			<string>wrapper.app-extension</string>
			<key>includeInIndex</key>
			<string>0</string>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>path</key>
			<string>FinPinIntentsExtensionUI.appex</string>
			<key>sourceTree</key>
			<string>BUILT_PRODUCTS_DIR</string>
		</dict>
		<key>D13028272E4EA4F3001C7F46</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>wrapper.framework</string>
			<key>name</key>
			<string>IntentsUI.framework</string>
			<key>path</key>
			<string>System/Library/Frameworks/IntentsUI.framework</string>
			<key>sourceTree</key>
			<string>SDKROOT</string>
		</dict>
		<key>D13028282E4EA4F3001C7F46</key>
		<dict>
			<key>fileRef</key>
			<string>D13028272E4EA4F3001C7F46</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>D13028292E4EA4F3001C7F46</key>
		<dict>
			<key>exceptions</key>
			<array>
				<string>D130283D2E4EA4F3001C7F46</string>
			</array>
			<key>explicitFileTypes</key>
			<dict/>
			<key>explicitFolders</key>
			<array/>
			<key>isa</key>
			<string>PBXFileSystemSynchronizedRootGroup</string>
			<key>path</key>
			<string>FinPinIntentsExtensionUI</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>D13028302E4EA4F3001C7F46</key>
		<dict>
			<key>containerPortal</key>
			<string>A1000027000000000000001</string>
			<key>isa</key>
			<string>PBXContainerItemProxy</string>
			<key>proxyType</key>
			<string>1</string>
			<key>remoteGlobalIDString</key>
			<string>D13028252E4EA4F3001C7F46</string>
			<key>remoteInfo</key>
			<string>FinPinIntentsExtensionUI</string>
		</dict>
		<key>D13028312E4EA4F3001C7F46</key>
		<dict>
			<key>isa</key>
			<string>PBXTargetDependency</string>
			<key>target</key>
			<string>D13028252E4EA4F3001C7F46</string>
			<key>targetProxy</key>
			<string>D13028302E4EA4F3001C7F46</string>
		</dict>
		<key>D13028322E4EA4F3001C7F46</key>
		<dict>
			<key>fileRef</key>
			<string>D13028262E4EA4F3001C7F46</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
			<key>settings</key>
			<dict>
				<key>ATTRIBUTES</key>
				<array>
					<string>RemoveHeadersOnCopy</string>
				</array>
			</dict>
		</dict>
		<key>D13028332E4EA4F3001C7F46</key>
		<dict>
			<key>containerPortal</key>
			<string>A1000027000000000000001</string>
			<key>isa</key>
			<string>PBXContainerItemProxy</string>
			<key>proxyType</key>
			<string>1</string>
			<key>remoteGlobalIDString</key>
			<string>D13028192E4EA4F2001C7F46</string>
			<key>remoteInfo</key>
			<string>FinPinIntentsExtension</string>
		</dict>
		<key>D13028342E4EA4F3001C7F46</key>
		<dict>
			<key>isa</key>
			<string>PBXTargetDependency</string>
			<key>target</key>
			<string>D13028192E4EA4F2001C7F46</string>
			<key>targetProxy</key>
			<string>D13028332E4EA4F3001C7F46</string>
		</dict>
		<key>D13028352E4EA4F3001C7F46</key>
		<dict>
			<key>fileRef</key>
			<string>D130281A2E4EA4F2001C7F46</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
			<key>settings</key>
			<dict>
				<key>ATTRIBUTES</key>
				<array>
					<string>RemoveHeadersOnCopy</string>
				</array>
			</dict>
		</dict>
		<key>D13028362E4EA4F3001C7F46</key>
		<dict>
			<key>buildActionMask</key>
			<string>**********</string>
			<key>dstPath</key>
			<string></string>
			<key>dstSubfolderSpec</key>
			<string>13</string>
			<key>files</key>
			<array>
				<string>D13028352E4EA4F3001C7F46</string>
				<string>D13028322E4EA4F3001C7F46</string>
			</array>
			<key>isa</key>
			<string>PBXCopyFilesBuildPhase</string>
			<key>name</key>
			<string>Embed Foundation Extensions</string>
			<key>runOnlyForDeploymentPostprocessing</key>
			<string>0</string>
		</dict>
		<key>D13028372E4EA4F3001C7F46</key>
		<dict>
			<key>buildSettings</key>
			<dict>
				<key>CODE_SIGN_IDENTITY</key>
				<string>Apple Development</string>
				<key>CODE_SIGN_STYLE</key>
				<string>Automatic</string>
				<key>CURRENT_PROJECT_VERSION</key>
				<string>1</string>
				<key>DEVELOPMENT_TEAM</key>
				<string>F8KSY9YZVT</string>
				<key>GENERATE_INFOPLIST_FILE</key>
				<string>YES</string>
				<key>INFOPLIST_FILE</key>
				<string>FinPinIntentsExtension/Info.plist</string>
				<key>INFOPLIST_KEY_CFBundleDisplayName</key>
				<string>FinPinIntentsExtension</string>
				<key>INFOPLIST_KEY_NSHumanReadableCopyright</key>
				<string></string>
				<key>IPHONEOS_DEPLOYMENT_TARGET</key>
				<string>16.6</string>
				<key>LD_RUNPATH_SEARCH_PATHS</key>
				<array>
					<string>$(inherited)</string>
					<string>@executable_path/Frameworks</string>
					<string>@executable_path/../../Frameworks</string>
				</array>
				<key>MARKETING_VERSION</key>
				<string>1.0</string>
				<key>PRODUCT_BUNDLE_IDENTIFIER</key>
				<string>com.finpin.app.FinPinIntentsExtension</string>
				<key>PRODUCT_NAME</key>
				<string>$(TARGET_NAME)</string>
				<key>SKIP_INSTALL</key>
				<string>YES</string>
				<key>SWIFT_EMIT_LOC_STRINGS</key>
				<string>YES</string>
				<key>SWIFT_VERSION</key>
				<string>5.0</string>
				<key>TARGETED_DEVICE_FAMILY</key>
				<string>1,2</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Debug</string>
		</dict>
		<key>D13028382E4EA4F3001C7F46</key>
		<dict>
			<key>buildSettings</key>
			<dict>
				<key>CODE_SIGN_STYLE</key>
				<string>Automatic</string>
				<key>CURRENT_PROJECT_VERSION</key>
				<string>1</string>
				<key>DEVELOPMENT_TEAM</key>
				<string>F8KSY9YZVT</string>
				<key>GENERATE_INFOPLIST_FILE</key>
				<string>YES</string>
				<key>INFOPLIST_FILE</key>
				<string>FinPinIntentsExtension/Info.plist</string>
				<key>INFOPLIST_KEY_CFBundleDisplayName</key>
				<string>FinPinIntentsExtension</string>
				<key>INFOPLIST_KEY_NSHumanReadableCopyright</key>
				<string></string>
				<key>IPHONEOS_DEPLOYMENT_TARGET</key>
				<string>16.6</string>
				<key>LD_RUNPATH_SEARCH_PATHS</key>
				<array>
					<string>$(inherited)</string>
					<string>@executable_path/Frameworks</string>
					<string>@executable_path/../../Frameworks</string>
				</array>
				<key>MARKETING_VERSION</key>
				<string>1.0</string>
				<key>PRODUCT_BUNDLE_IDENTIFIER</key>
				<string>com.finpin.app.FinPinIntentsExtension</string>
				<key>PRODUCT_NAME</key>
				<string>$(TARGET_NAME)</string>
				<key>SKIP_INSTALL</key>
				<string>YES</string>
				<key>SWIFT_EMIT_LOC_STRINGS</key>
				<string>YES</string>
				<key>SWIFT_VERSION</key>
				<string>5.0</string>
				<key>TARGETED_DEVICE_FAMILY</key>
				<string>1,2</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Release</string>
		</dict>
		<key>D13028392E4EA4F3001C7F46</key>
		<dict>
			<key>buildSettings</key>
			<dict>
				<key>CODE_SIGN_IDENTITY</key>
				<string>Apple Development</string>
				<key>CODE_SIGN_STYLE</key>
				<string>Automatic</string>
				<key>CURRENT_PROJECT_VERSION</key>
				<string>1</string>
				<key>DEVELOPMENT_TEAM</key>
				<string>F8KSY9YZVT</string>
				<key>GENERATE_INFOPLIST_FILE</key>
				<string>YES</string>
				<key>INFOPLIST_FILE</key>
				<string>FinPinIntentsExtensionUI/Info.plist</string>
				<key>INFOPLIST_KEY_CFBundleDisplayName</key>
				<string>FinPinIntentsExtensionUI</string>
				<key>INFOPLIST_KEY_NSHumanReadableCopyright</key>
				<string></string>
				<key>IPHONEOS_DEPLOYMENT_TARGET</key>
				<string>16.6</string>
				<key>LD_RUNPATH_SEARCH_PATHS</key>
				<array>
					<string>$(inherited)</string>
					<string>@executable_path/Frameworks</string>
					<string>@executable_path/../../Frameworks</string>
				</array>
				<key>MARKETING_VERSION</key>
				<string>1.0</string>
				<key>PRODUCT_BUNDLE_IDENTIFIER</key>
				<string>com.finpin.app.FinPinIntentsExtensionUI</string>
				<key>PRODUCT_NAME</key>
				<string>$(TARGET_NAME)</string>
				<key>SKIP_INSTALL</key>
				<string>YES</string>
				<key>SWIFT_EMIT_LOC_STRINGS</key>
				<string>YES</string>
				<key>SWIFT_VERSION</key>
				<string>5.0</string>
				<key>TARGETED_DEVICE_FAMILY</key>
				<string>1,2</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Debug</string>
		</dict>
		<key>D130283A2E4EA4F3001C7F46</key>
		<dict>
			<key>buildSettings</key>
			<dict>
				<key>CODE_SIGN_STYLE</key>
				<string>Automatic</string>
				<key>CURRENT_PROJECT_VERSION</key>
				<string>1</string>
				<key>DEVELOPMENT_TEAM</key>
				<string>F8KSY9YZVT</string>
				<key>GENERATE_INFOPLIST_FILE</key>
				<string>YES</string>
				<key>INFOPLIST_FILE</key>
				<string>FinPinIntentsExtensionUI/Info.plist</string>
				<key>INFOPLIST_KEY_CFBundleDisplayName</key>
				<string>FinPinIntentsExtensionUI</string>
				<key>INFOPLIST_KEY_NSHumanReadableCopyright</key>
				<string></string>
				<key>IPHONEOS_DEPLOYMENT_TARGET</key>
				<string>16.6</string>
				<key>LD_RUNPATH_SEARCH_PATHS</key>
				<array>
					<string>$(inherited)</string>
					<string>@executable_path/Frameworks</string>
					<string>@executable_path/../../Frameworks</string>
				</array>
				<key>MARKETING_VERSION</key>
				<string>1.0</string>
				<key>PRODUCT_BUNDLE_IDENTIFIER</key>
				<string>com.finpin.app.FinPinIntentsExtensionUI</string>
				<key>PRODUCT_NAME</key>
				<string>$(TARGET_NAME)</string>
				<key>SKIP_INSTALL</key>
				<string>YES</string>
				<key>SWIFT_EMIT_LOC_STRINGS</key>
				<string>YES</string>
				<key>SWIFT_VERSION</key>
				<string>5.0</string>
				<key>TARGETED_DEVICE_FAMILY</key>
				<string>1,2</string>
			</dict>
			<key>isa</key>
			<string>XCBuildConfiguration</string>
			<key>name</key>
			<string>Release</string>
		</dict>
		<key>D130283B2E4EA4F3001C7F46</key>
		<dict>
			<key>isa</key>
			<string>PBXFileSystemSynchronizedBuildFileExceptionSet</string>
			<key>membershipExceptions</key>
			<array>
				<string>Info.plist</string>
			</array>
			<key>target</key>
			<string>D13028192E4EA4F2001C7F46</string>
		</dict>
		<key>D130283C2E4EA4F3001C7F46</key>
		<dict>
			<key>buildConfigurations</key>
			<array>
				<string>D13028372E4EA4F3001C7F46</string>
				<string>D13028382E4EA4F3001C7F46</string>
			</array>
			<key>defaultConfigurationIsVisible</key>
			<string>0</string>
			<key>defaultConfigurationName</key>
			<string>Release</string>
			<key>isa</key>
			<string>XCConfigurationList</string>
		</dict>
		<key>D130283D2E4EA4F3001C7F46</key>
		<dict>
			<key>isa</key>
			<string>PBXFileSystemSynchronizedBuildFileExceptionSet</string>
			<key>membershipExceptions</key>
			<array>
				<string>Info.plist</string>
			</array>
			<key>target</key>
			<string>D13028252E4EA4F3001C7F46</string>
		</dict>
		<key>D130283E2E4EA4F3001C7F46</key>
		<dict>
			<key>buildConfigurations</key>
			<array>
				<string>D13028392E4EA4F3001C7F46</string>
				<string>D130283A2E4EA4F3001C7F46</string>
			</array>
			<key>defaultConfigurationIsVisible</key>
			<string>0</string>
			<key>defaultConfigurationName</key>
			<string>Release</string>
			<key>isa</key>
			<string>XCConfigurationList</string>
		</dict>
		<key>D18B7D04CD7EF943B7001C7F46</key>
		<dict>
			<key>fileRef</key>
			<string>D18B7D04CD7EF943B8001C7F46</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>D18B7D04CD7EF943B8001C7F46</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>file.intentdefinition</string>
			<key>path</key>
			<string>AddExpenseIntent.intentdefinition</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>D18B7D04CD7EF943B9001C7F46</key>
		<dict>
			<key>children</key>
			<array>
				<string>D18B7D04CD7EF943B8001C7F46</string>
			</array>
			<key>isa</key>
			<string>PBXGroup</string>
			<key>path</key>
			<string>Intents</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
		<key>D1968DD42E3A448A001DF072</key>
		<dict>
			<key>fileRef</key>
			<string>D1968DD42E3A448A001DF072</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>D1968DD52E3A448A001DF072</key>
		<dict>
			<key>fileRef</key>
			<string>D1968DD42E3A448A001DF072</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>D1968DD62E3A448A001DF073</key>
		<dict>
			<key>fileRef</key>
			<string>D1968DD72E3A448A001DF073</string>
			<key>isa</key>
			<string>PBXBuildFile</string>
		</dict>
		<key>D1968DD72E3A448A001DF073</key>
		<dict>
			<key>isa</key>
			<string>PBXFileReference</string>
			<key>lastKnownFileType</key>
			<string>sourcecode.swift</string>
			<key>path</key>
			<string>IntentHandler.swift</string>
			<key>sourceTree</key>
			<string>&lt;group&gt;</string>
		</dict>
	</dict>
	<key>rootObject</key>
	<string>A1000027000000000000001</string>
</dict>
</plist>
