# FinPin Serverless API Environment Variables
# Copy this file to .env and fill in your values

# =============================================================================
# AI Provider Configuration (choose one)
# =============================================================================

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_BASE_URL=https://api.openai.com/v1

# ARK Configuration (ByteDance - alternative to OpenAI)
# ARK_API_KEY=your-ark-api-key-here
# ARK_MODEL=doubao-1-5-lite-32k-250115
# ARK_BASE_URL=https://ark.cn-beijing.volces.com/api/v3

# Other OpenAI-compatible APIs
# OPENAI_API_KEY=your-api-key-here
# OPENAI_MODEL=your-model-name
# OPENAI_BASE_URL=https://your-api-provider.com/v1

# =============================================================================
# Security Configuration
# =============================================================================

# Random seed for device key generation (32+ characters recommended)
MASTER_KEY_SEED=your-random-master-key-seed-32-characters-minimum

# Secret for JWT token signing (32+ characters recommended)
JWT_SECRET=your-jwt-secret-32-characters-minimum

# =============================================================================
# API Configuration
# =============================================================================

# Environment: development, staging, production
ENVIRONMENT=development

# API version
API_VERSION=v1

# Rate limiting (requests per minute per device)
RATE_LIMIT_PER_MINUTE=10

# Request timeout in seconds
REQUEST_TIMEOUT_SECONDS=30

# HMAC signature validity in minutes
SIGNATURE_VALIDITY_MINUTES=5

# =============================================================================
# Cloudflare KV Namespace IDs
# =============================================================================
# Create these using:
# wrangler kv:namespace create "CACHE"
# wrangler kv:namespace create "RATE_LIMIT"
# wrangler kv:namespace create "CACHE" --preview
# wrangler kv:namespace create "RATE_LIMIT" --preview

# Cache KV namespace
CACHE_KV_ID=your-cache-kv-namespace-id
CACHE_KV_PREVIEW_ID=your-cache-preview-id

# Rate limit KV namespace
RATE_LIMIT_KV_ID=your-rate-limit-kv-namespace-id
RATE_LIMIT_KV_PREVIEW_ID=your-rate-limit-preview-id

# =============================================================================
# Development Configuration
# =============================================================================

# Local development port (for wrangler dev)
PORT=8787

# Enable debug logging
DEBUG=true
