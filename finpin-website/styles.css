/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo h1 {
    color: #007AFF;
    font-size: 1.8rem;
    font-weight: 700;
}

.logo .tagline {
    color: #666;
    font-size: 0.9rem;
    margin-left: 0.5rem;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: #007AFF;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.hero-content {
    max-width: 600px;
}

.hero h2 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
}

.hero p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s;
    display: inline-block;
}

.btn-primary {
    background: #007AFF;
    color: white;
}

.btn-primary:hover {
    background: #0056CC;
    transform: translateY(-2px);
}

.btn-secondary {
    background: white;
    color: #007AFF;
    border: 2px solid #007AFF;
}

.btn-secondary:hover {
    background: #007AFF;
    color: white;
}

/* App Mockup */
.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
}

.phone-frame {
    width: 250px;
    height: 500px;
    background: #333;
    border-radius: 30px;
    padding: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.screen {
    width: 100%;
    height: 100%;
    background: #000;
    border-radius: 20px;
    overflow: hidden;
}

.app-interface {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100%;
    padding: 20px;
    color: white;
}

.status-bar {
    height: 20px;
    background: rgba(255,255,255,0.2);
    border-radius: 10px;
    margin-bottom: 20px;
}

.app-header {
    font-size: 1.5rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30px;
}

.expense-card {
    background: rgba(255,255,255,0.2);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.expense-amount {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.expense-category {
    font-size: 1rem;
    opacity: 0.8;
    margin-bottom: 5px;
}

.expense-date {
    font-size: 0.9rem;
    opacity: 0.6;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: #fff;
}

.features h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 12px;
    background: #f8f9fa;
    transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Download Section */
.download {
    padding: 80px 0;
    background: #f8f9fa;
    text-align: center;
}

.download h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.download p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
}

.download-buttons {
    margin-bottom: 2rem;
}

.app-store-btn img {
    height: 60px;
    transition: transform 0.3s;
}

.app-store-btn:hover img {
    transform: scale(1.05);
}

.download-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.info-item .label {
    font-weight: 600;
    color: #666;
    margin-bottom: 0.5rem;
}

.info-item .value {
    color: #333;
    font-weight: 500;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #007AFF;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: #007AFF;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #555;
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-links {
        gap: 1rem;
    }
    
    .hero h2 {
        font-size: 2rem;
    }
    
    .cta-buttons {
        flex-direction: column;
    }
    
    .phone-frame {
        width: 200px;
        height: 400px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .download-info {
        flex-direction: column;
        gap: 1rem;
    }
}
