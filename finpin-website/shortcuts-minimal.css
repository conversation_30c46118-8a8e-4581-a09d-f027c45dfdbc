/* FinPin Shortcuts - Minimal Geek Style */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    background: #000;
    color: #fff;
    line-height: 1.4;
    font-size: 14px;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    border-bottom: 1px solid #333;
    padding-bottom: 20px;
}

header h1 {
    font-size: 24px;
    font-weight: 400;
    margin-bottom: 8px;
}

header p {
    color: #888;
    font-size: 12px;
}

main {
    margin-bottom: 40px;
}

.shortcuts-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 32px;
}

.shortcut-item {
    background: #111;
    border: 1px solid #333;
    padding: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.shortcut-info {
    flex: 1;
}

.shortcut-info h3 {
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 8px;
    color: #fff;
}

.shortcut-info p {
    color: #ccc;
    font-size: 13px;
    line-height: 1.4;
}

.download-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #fff;
    color: #000;
    text-decoration: none;
    padding: 10px 16px;
    border: none;
    font-family: inherit;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.download-btn:hover {
    background: #f0f0f0;
    transform: translateY(-1px);
}

.download-icon {
    font-size: 16px;
    font-weight: bold;
}

.setup {
    background: #0a0a0a;
    border: 1px solid #222;
    padding: 20px;
}

.setup h3 {
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 16px;
    color: #fff;
}

.setup ol {
    list-style: none;
    counter-reset: step-counter;
}

.setup li {
    counter-increment: step-counter;
    margin-bottom: 8px;
    padding-left: 24px;
    position: relative;
    color: #ccc;
    font-size: 13px;
}

.setup li:before {
    content: counter(step-counter);
    position: absolute;
    left: 0;
    top: 0;
    background: #333;
    color: #fff;
    width: 16px;
    height: 16px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 500;
}

footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #333;
}

footer a {
    color: #888;
    text-decoration: none;
    font-size: 12px;
    transition: color 0.2s ease;
}

footer a:hover {
    color: #fff;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
    .container {
        padding: 16px;
    }

    header h1 {
        font-size: 20px;
    }

    .shortcut-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        padding: 20px;
    }

    .download-btn {
        align-self: stretch;
        justify-content: center;
    }

    .setup {
        padding: 16px;
    }
}