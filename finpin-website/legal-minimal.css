/* Legal Pages - <PERSON><PERSON> Style */

.legal-content {
    padding-top: 80px;
    min-height: 100vh;
    background: #fafafa;
}

.legal-header {
    text-align: center;
    padding: 40px 0;
    background: #fff;
    margin-bottom: 32px;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
}

.legal-header h1 {
    font-size: 24px;
    color: #000;
    margin-bottom: 8px;
    font-weight: 500;
    text-transform: lowercase;
    letter-spacing: -0.5px;
}

.effective-date {
    font-size: 12px;
    color: #666;
    font-weight: 400;
}

.legal-body {
    background: #fff;
    padding: 32px;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    margin-bottom: 32px;
    line-height: 1.4;
}

.legal-body section {
    margin-bottom: 32px;
}

.legal-body section:last-child {
    margin-bottom: 0;
}

.legal-body h2 {
    font-size: 16px;
    color: #000;
    margin-bottom: 12px;
    font-weight: 500;
    text-transform: lowercase;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
}

.legal-body h3 {
    font-size: 14px;
    color: #333;
    margin: 16px 0 8px 0;
    font-weight: 500;
    text-transform: lowercase;
}

.legal-body p {
    margin-bottom: 12px;
    color: #333;
    font-size: 13px;
}

.legal-body ul {
    margin: 12px 0 12px 20px;
    padding: 0;
}

.legal-body li {
    margin-bottom: 6px;
    color: #333;
    font-size: 13px;
}

.legal-body a {
    color: #000;
    text-decoration: underline;
    font-weight: 500;
}

.legal-body a:hover {
    color: #666;
}

.highlight-box {
    background: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 16px;
    margin: 16px 0;
}

.highlight-box ul {
    margin: 0;
}

.highlight-box li {
    color: #000;
    font-weight: 500;
}

.legal-footer {
    background: #fff;
    padding: 24px 32px;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    text-align: center;
}

.legal-footer p {
    color: #666;
    margin-bottom: 16px;
    font-size: 12px;
}

.legal-nav {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
}

.legal-nav a {
    color: #666;
    text-decoration: none;
    font-weight: 400;
    padding: 6px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    transition: all 0.2s;
    font-size: 12px;
    text-transform: lowercase;
}

.legal-nav a:hover {
    background: #f5f5f5;
    color: #000;
}

/* Navigation Active State */
.nav-links a.active {
    color: #000;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .legal-content {
        padding-top: 60px;
    }
    
    .legal-body {
        padding: 20px;
    }
    
    .legal-header {
        padding: 24px 0;
    }
    
    .legal-header h1 {
        font-size: 20px;
    }
    
    .legal-body h2 {
        font-size: 14px;
    }
    
    .legal-nav {
        flex-direction: column;
        gap: 8px;
    }
    
    .legal-nav a {
        display: block;
        text-align: center;
    }
    
    .legal-footer {
        padding: 16px 20px;
    }
}
