/* FinPin Minimal Design */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    line-height: 1.4;
    color: #1a1a1a;
    background: #fafafa;
    font-size: 14px;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 16px;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(250, 250, 250, 0.9);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid #e0e0e0;
    z-index: 1000;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
}

.logo h1 {
    font-size: 18px;
    font-weight: 600;
    color: #000;
    margin: 0;
    letter-spacing: -0.5px;
}

.tagline {
    font-size: 10px;
    color: #666;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-links {
    display: flex;
    gap: 24px;
}

.nav-links a {
    text-decoration: none;
    color: #666;
    font-weight: 400;
    font-size: 13px;
    transition: color 0.2s;
    text-transform: lowercase;
}

.nav-links a:hover {
    color: #000;
}

/* Hero Section */
.hero {
    padding: 80px 0 60px;
    text-align: center;
    background: #fafafa;
}

.hero-title {
    font-size: 32px;
    font-weight: 500;
    color: #000;
    margin-bottom: 8px;
    line-height: 1.2;
    letter-spacing: -1px;
}

.hero-subtitle {
    font-size: 16px;
    color: #666;
    font-weight: 400;
    margin-bottom: 16px;
    text-transform: lowercase;
    letter-spacing: 0.5px;
}

.hero-description {
    font-size: 14px;
    color: #666;
    max-width: 480px;
    margin: 0 auto 32px;
    line-height: 1.4;
}

.cta-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 400;
    font-size: 13px;
    transition: all 0.2s;
    border: 1px solid #e0e0e0;
    cursor: pointer;
    text-transform: lowercase;
}

.btn-primary {
    background: #000;
    color: #fff;
    border-color: #000;
}

.btn-primary:hover {
    background: #333;
}

.btn-secondary {
    background: #fff;
    color: #666;
    border-color: #e0e0e0;
}

.btn-secondary:hover {
    background: #f5f5f5;
    color: #000;
}

.btn-outline {
    background: transparent;
    color: #666;
    border-color: #ccc;
}

.btn-outline:hover {
    background: #f9f9f9;
    color: #000;
    border-color: #999;
}

/* Features Section */
.features {
    padding: 60px 0;
    background: #fff;
}

.features h2 {
    text-align: center;
    font-size: 20px;
    font-weight: 500;
    color: #000;
    margin-bottom: 40px;
    text-transform: lowercase;
    letter-spacing: -0.5px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
}

.feature-card {
    padding: 20px;
    border-radius: 6px;
    background: #fafafa;
    border: 1px solid #e8e8e8;
    transition: all 0.2s;
}

.feature-card:hover {
    background: #f5f5f5;
    border-color: #d0d0d0;
}

.feature-card.highlight {
    background: #000;
    color: #fff;
    border-color: #000;
}

.feature-card.highlight:hover {
    background: #333;
}

.feature-icon {
    font-size: 16px;
    margin-bottom: 12px;
    display: block;
    opacity: 0.7;
}

.feature-card h3 {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: inherit;
    text-transform: lowercase;
}

.feature-card p {
    color: inherit;
    opacity: 0.7;
    line-height: 1.4;
    font-size: 12px;
}

/* Download Section */
.download {
    padding: 60px 0;
    background: #fafafa;
    text-align: center;
}

.download h2 {
    font-size: 20px;
    font-weight: 500;
    color: #000;
    margin-bottom: 12px;
    text-transform: lowercase;
    letter-spacing: -0.5px;
}

.download-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 24px;
}

.download-buttons {
    margin-bottom: 24px;
}

.app-store-btn img {
    height: 40px;
    transition: opacity 0.2s;
}

.app-store-btn:hover img {
    opacity: 0.8;
}

.download-features {
    display: flex;
    justify-content: center;
    gap: 24px;
    flex-wrap: wrap;
    margin-top: 16px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 400;
    color: #666;
    font-size: 12px;
}

.feature-check {
    color: #000;
    font-weight: 500;
    font-size: 10px;
}

/* Footer */
.footer {
    padding: 32px 0;
    background: #000;
    color: #fff;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.footer-main h3 {
    color: #fff;
    font-size: 16px;
    margin-bottom: 4px;
    font-weight: 500;
}

.footer-main p {
    color: #666;
    font-size: 11px;
    text-transform: lowercase;
}

.footer-links {
    display: flex;
    gap: 16px;
}

.footer-links a {
    color: #666;
    text-decoration: none;
    font-weight: 400;
    transition: color 0.2s;
    font-size: 12px;
    text-transform: lowercase;
}

.footer-links a:hover {
    color: #fff;
}

.footer-bottom {
    text-align: center;
    padding-top: 16px;
    border-top: 1px solid #333;
}

.footer-bottom p {
    color: #666;
    font-size: 10px;
    text-transform: lowercase;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 12px;
    }

    .hero {
        padding: 100px 0 40px;
    }

    .hero-title {
        font-size: 24px;
    }

    .hero-subtitle {
        font-size: 14px;
    }

    .hero-description {
        font-size: 13px;
        margin-bottom: 24px;
    }

    .nav-links {
        display: none;
    }

    .features {
        padding: 40px 0;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .download {
        padding: 40px 0;
    }

    .footer {
        padding: 24px 0;
    }

    .footer-content {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 12px;
    }

    .download-features {
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }
}
