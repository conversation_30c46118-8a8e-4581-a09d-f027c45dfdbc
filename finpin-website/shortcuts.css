/* Shortcuts Page Styles */

.shortcuts-content {
    padding-top: 100px;
    min-height: 100vh;
    background: #f8f9fa;
}

.shortcuts-header {
    text-align: center;
    padding: 40px 0;
    background: white;
    margin-bottom: 40px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.shortcuts-header h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 1rem;
}

.subtitle {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Intro Cards */
.shortcuts-intro {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 40px;
}

.intro-card {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s;
}

.intro-card:hover {
    transform: translateY(-5px);
}

.intro-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.intro-card h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1rem;
}

.intro-card p {
    color: #666;
    line-height: 1.6;
}

/* Shortcut Item */
.shortcuts-list {
    margin-bottom: 40px;
}

.shortcut-item {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.shortcut-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex: 1;
}

.shortcut-icon img {
    width: 80px;
    height: 80px;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.shortcut-details h3 {
    font-size: 1.4rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.shortcut-details p {
    color: #666;
    margin-bottom: 1rem;
}

.shortcut-features {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.feature-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.shortcut-actions {
    text-align: center;
}

.download-btn {
    background: #007AFF;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s;
}

.download-btn:hover {
    background: #0056CC;
    transform: translateY(-2px);
}

.download-placeholder {
    background: #f5f5f5;
    border: 2px dashed #ccc;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
}

.download-placeholder p {
    margin: 0 0 5px 0;
    color: #666;
    font-weight: 500;
}

.download-placeholder small {
    color: #999;
}

/* Setup Guide */
.setup-guide {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 40px;
}

.setup-guide h2 {
    text-align: center;
    font-size: 2rem;
    color: #333;
    margin-bottom: 2rem;
}

.guide-steps {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.step {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.step-number {
    background: #007AFF;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
}

.step-content h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.step-content p {
    color: #666;
    margin-bottom: 1rem;
}

.step-link {
    color: #007AFF;
    text-decoration: none;
    font-weight: 500;
}

.step-link:hover {
    text-decoration: underline;
}

.siri-examples {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.siri-phrase {
    background: #f0f0f0;
    color: #333;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    border: 1px solid #ddd;
}

/* Usage Examples */
.usage-examples {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 40px;
}

.usage-examples h2 {
    text-align: center;
    font-size: 2rem;
    color: #333;
    margin-bottom: 2rem;
}

.example-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.example-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.example-header {
    background: #f5f5f5;
    padding: 12px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.example-type {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.example-content {
    padding: 20px;
}

.example-input {
    background: #e3f2fd;
    color: #1976d2;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 10px;
    font-weight: 500;
}

.example-response {
    background: #f3e5f5;
    color: #7b1fa2;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 10px;
}

.example-result {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 10px;
    border-radius: 6px;
    font-weight: 500;
}

/* FAQ Section */
.faq-section {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.faq-section h2 {
    text-align: center;
    font-size: 2rem;
    color: #333;
    margin-bottom: 2rem;
}

.faq-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.faq-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
}

.faq-item h3 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.faq-item p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .shortcut-item {
        flex-direction: column;
        text-align: center;
    }
    
    .shortcut-info {
        flex-direction: column;
        text-align: center;
    }
    
    .step {
        flex-direction: column;
        text-align: center;
    }
    
    .step-number {
        align-self: center;
    }
    
    .example-grid {
        grid-template-columns: 1fr;
    }
    
    .siri-examples {
        justify-content: center;
    }
}
