# FinPin Website

FinPin官方网站，包含应用介绍、法律文档和快捷指令下载。

## 📁 项目结构

```
finpin-website/
├── index.html          # 主页
├── terms.html          # 用户协议页面
├── privacy.html        # 隐私政策页面
├── shortcuts.html      # 快捷指令下载页面
├── styles.css          # 主样式文件
├── legal.css           # 法律页面样式
├── shortcuts.css       # 快捷指令页面样式
├── _headers            # Cloudflare Pages头部配置
├── _redirects          # Cloudflare Pages重定向配置
└── README.md           # 项目说明
```

## 🌐 页面说明

### 主页 (index.html)
- 应用介绍和功能特色
- 下载链接（待App Store上架后更新）
- 响应式设计，支持移动端

### 用户协议 (terms.html)
- 完整的服务条款
- 基于FinPin应用的实际功能
- 符合App Store要求

### 隐私政策 (privacy.html)
- 详细的隐私保护说明
- 强调本地存储和数据安全
- 符合GDPR和相关法规要求

### 快捷指令页面 (shortcuts.html)
- 快捷指令下载和使用指南
- 设置步骤和使用示例
- 常见问题解答

## 🚀 部署到Cloudflare Pages

### 方法1: Git集成部署
1. 将此目录推送到Git仓库
2. 在Cloudflare Pages中连接仓库
3. 设置构建配置：
   - 构建命令: 无需构建
   - 输出目录: `/`
   - 根目录: `finpin-website`

### 方法2: 直接上传
1. 登录Cloudflare Pages
2. 创建新项目
3. 上传整个finpin-website目录
4. 设置自定义域名（可选）

## 🔧 配置说明

### 自定义域名
建议使用以下域名结构：
- 主域名: `finpin.app`
- 子域名: `www.finpin.app`

### SSL/TLS
Cloudflare Pages自动提供SSL证书，确保HTTPS访问。

### 缓存配置
通过`_headers`文件配置了适当的缓存策略：
- HTML文件: 1小时
- CSS/JS文件: 1天
- 图片文件: 1周

## 📱 移动端优化

- 响应式设计，适配各种屏幕尺寸
- 触摸友好的交互元素
- 快速加载优化
- PWA就绪（可后续添加manifest.json）

## 🔗 URL结构

- 主页: `/`
- 用户协议: `/terms.html` 或 `/terms`
- 隐私政策: `/privacy.html` 或 `/privacy`
- 快捷指令: `/shortcuts.html` 或 `/shortcuts`

## 📝 待办事项

- [ ] 添加App Store下载链接（应用上架后）
- [ ] 创建实际的快捷指令文件
- [ ] 添加应用截图和图标
- [ ] 设置Google Analytics（可选）
- [ ] 添加联系表单（可选）
- [ ] 多语言支持（可选）

## 🛠️ 维护说明

### 更新内容
- 修改HTML文件中的相应内容
- 确保法律文档与应用实际功能一致
- 定期检查链接有效性

### 性能监控
- 使用Cloudflare Analytics监控访问情况
- 定期检查页面加载速度
- 监控移动端用户体验

## 📞 联系信息

如需更新网站内容或有技术问题，请联系：
- 邮箱: <EMAIL>
- 技术支持: 通过GitHub Issues
