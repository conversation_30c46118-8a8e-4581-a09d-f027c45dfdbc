/* Legal Pages Styles */

.legal-content {
    padding-top: 100px;
    min-height: 100vh;
    background: #f8f9fa;
}

.legal-header {
    text-align: center;
    padding: 40px 0;
    background: white;
    margin-bottom: 40px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.legal-header h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 1rem;
}

.effective-date {
    font-size: 1.1rem;
    color: #666;
}

.legal-body {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 40px;
    line-height: 1.8;
}

.legal-body section {
    margin-bottom: 3rem;
}

.legal-body h2 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #007AFF;
}

.legal-body h3 {
    font-size: 1.3rem;
    color: #444;
    margin: 2rem 0 1rem 0;
}

.legal-body p {
    margin-bottom: 1rem;
    color: #555;
}

.legal-body ul {
    margin: 1rem 0 1rem 2rem;
}

.legal-body li {
    margin-bottom: 0.5rem;
    color: #555;
}

.legal-body a {
    color: #007AFF;
    text-decoration: none;
}

.legal-body a:hover {
    text-decoration: underline;
}

.highlight-box {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 2px solid #007AFF;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.highlight-box ul {
    margin: 0;
}

.highlight-box li {
    color: #333;
    font-weight: 500;
}

.legal-footer {
    background: white;
    padding: 30px 40px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.legal-footer p {
    color: #666;
    margin-bottom: 20px;
}

.legal-nav {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.legal-nav a {
    color: #007AFF;
    text-decoration: none;
    font-weight: 500;
    padding: 8px 16px;
    border: 1px solid #007AFF;
    border-radius: 6px;
    transition: all 0.3s;
}

.legal-nav a:hover {
    background: #007AFF;
    color: white;
}

/* Navigation Active State */
.nav-links a.active {
    color: #007AFF;
    font-weight: 600;
}

.logo a {
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo a h1 {
    color: #007AFF;
}

.logo a .tagline {
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .legal-body {
        padding: 20px;
    }
    
    .legal-header h1 {
        font-size: 2rem;
    }
    
    .legal-body h2 {
        font-size: 1.5rem;
    }
    
    .legal-nav {
        flex-direction: column;
        gap: 1rem;
    }
    
    .legal-nav a {
        display: block;
        text-align: center;
    }
}
