name = "finpin-exchange-rates"
main = "exchange-rate-worker.js"
compatibility_date = "2024-01-01"

# KV namespace for caching exchange rates
[[kv_namespaces]]
binding = "FINPIN_KV"
id = "5f3c61d58fd546fab7fe1bcfb898581e"
preview_id = "2f198bf1785e4079aa02875d67d1e712"

# Environment variables
[env.production.vars]
ENVIRONMENT = "production"

[env.staging.vars]
ENVIRONMENT = "staging"

# Cron triggers for daily rate updates
[triggers]
crons = ["0 2 * * *"]  # Run daily at 2 AM UTC
