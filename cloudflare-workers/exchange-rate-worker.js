// Cloudflare Workers script for FinPin Exchange Rate API
// This worker fetches rates from OpenExchangeRates and caches them efficiently

const OPENEXCHANGERATES_APP_ID = '********************************';
const OPENEXCHANGERATES_BASE_URL = 'https://openexchangerates.org/api';

// Cache keys
const CACHE_KEY_PREFIX = 'finpin_rates_';
const LAST_SYNC_KEY = 'finpin_last_sync';

// Cache duration: 24 hours (since we sync daily)
const CACHE_DURATION = 24 * 60 * 60; // 24 hours in seconds

// Main currencies that need frequent updates
const MAIN_CURRENCIES = [
  'USD', 'EUR', 'GBP', 'JPY', 'CNY', 'CAD', 'AUD', 'CHF', 
  'KRW', 'SGD', 'HKD', 'INR', 'THB', 'MXN', 'BRL', 'NOK', 
  'SEK', 'DK<PERSON>', '<PERSON>L<PERSON>', 'CZ<PERSON>', 'RUB', 'TRY', 'ZAR', 'NZD'
];

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request));
});

async function handleRequest(request) {
  const url = new URL(request.url);
  
  // CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, User-Agent',
  };

  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  // Route handling
  if (url.pathname === '/api/rates') {
    return handleRatesRequest(request, corsHeaders);
  } else if (url.pathname === '/api/sync') {
    return handleSyncRequest(request, corsHeaders);
  } else if (url.pathname === '/api/status') {
    return handleStatusRequest(request, corsHeaders);
  }

  return new Response('Not Found', { status: 404, headers: corsHeaders });
}

async function handleRatesRequest(request, corsHeaders) {
  try {
    const url = new URL(request.url);
    const baseCurrency = url.searchParams.get('base') || 'USD';

    console.log(`📊 Fetching rates for base currency: ${baseCurrency}`);

    // Get USD-based rates from cache
    const usdRates = await getCachedRates('USD');

    if (usdRates) {
      // Convert USD-based rates to requested base currency
      const convertedRates = convertRatesFromUSD(usdRates, baseCurrency);
      console.log(`✅ Returning converted rates for ${baseCurrency}`);

      return new Response(JSON.stringify(convertedRates), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // If no cache, fetch fresh USD rates from OpenExchangeRates
    console.log(`🔄 Cache miss, fetching fresh USD rates`);
    const freshRates = await fetchFreshRates('USD');

    if (freshRates) {
      // Cache the USD rates
      await cacheRates('USD', freshRates);

      // Convert to requested base currency
      const convertedRates = convertRatesFromUSD(freshRates, baseCurrency);

      return new Response(JSON.stringify(convertedRates), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // Fallback to default rates
    const fallbackRates = getFallbackRates(baseCurrency);
    return new Response(JSON.stringify(fallbackRates), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('❌ Error in handleRatesRequest:', error);

    const errorResponse = {
      success: false,
      error: 'Internal server error',
      message: error.message
    };

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

async function handleSyncRequest(request, corsHeaders) {
  try {
    console.log('🔄 Manual sync requested');

    // OpenExchangeRates free plan only supports USD as base currency
    // So we fetch USD-based rates and cache them
    const rates = await fetchFreshRates('USD', true); // force refresh

    if (rates) {
      await cacheRates('USD', rates);

      // Update last sync time
      await FINPIN_KV.put(LAST_SYNC_KEY, new Date().toISOString());

      const response = {
        success: true,
        message: 'Sync completed successfully',
        base: 'USD',
        ratesCount: Object.keys(rates.rates).length,
        timestamp: Date.now()
      };

      return new Response(JSON.stringify(response), {
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    } else {
      throw new Error('Failed to fetch rates from OpenExchangeRates');
    }

  } catch (error) {
    console.error('❌ Error in handleSyncRequest:', error);

    return new Response(JSON.stringify({
      success: false,
      error: 'Sync failed',
      message: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

async function handleStatusRequest(request, corsHeaders) {
  try {
    const lastSync = await FINPIN_KV.get(LAST_SYNC_KEY);
    const cacheStats = {};
    
    // Check cache status for main currencies
    for (const currency of MAIN_CURRENCIES.slice(0, 5)) { // Check first 5
      const cached = await FINPIN_KV.get(`${CACHE_KEY_PREFIX}${currency}`);
      cacheStats[currency] = cached ? 'cached' : 'missing';
    }
    
    const response = {
      success: true,
      status: 'operational',
      lastSync: lastSync || 'never',
      cacheStats,
      timestamp: Date.now()
    };
    
    return new Response(JSON.stringify(response), {
      headers: { 
        'Content-Type': 'application/json',
        ...corsHeaders 
      }
    });
    
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      error: 'Status check failed',
      message: error.message
    }), {
      status: 500,
      headers: { 
        'Content-Type': 'application/json',
        ...corsHeaders 
      }
    });
  }
}

async function getCachedRates(baseCurrency) {
  try {
    const cacheKey = `${CACHE_KEY_PREFIX}${baseCurrency}`;
    const cached = await FINPIN_KV.get(cacheKey);
    
    if (cached) {
      const data = JSON.parse(cached);
      
      // Check if cache is still valid (24 hours)
      const cacheAge = Date.now() - data.timestamp;
      if (cacheAge < CACHE_DURATION * 1000) {
        console.log(`📊 Cache hit for ${baseCurrency}, age: ${Math.round(cacheAge / 1000 / 60)} minutes`);
        return data;
      } else {
        console.log(`⏰ Cache expired for ${baseCurrency}, age: ${Math.round(cacheAge / 1000 / 3600)} hours`);
      }
    }
    
    return null;
  } catch (error) {
    console.error('❌ Error getting cached rates:', error);
    return null;
  }
}

async function fetchFreshRates(baseCurrency = 'USD', forceRefresh = false) {
  try {
    // OpenExchangeRates free plan only supports USD as base currency
    const url = `${OPENEXCHANGERATES_BASE_URL}/latest.json?app_id=${OPENEXCHANGERATES_APP_ID}`;

    console.log(`🌐 Fetching from OpenExchangeRates: USD base`);

    const response = await fetch(url, {
      headers: {
        'User-Agent': 'FinPin-CloudflareWorker/1.0'
      }
    });

    if (!response.ok) {
      throw new Error(`OpenExchangeRates API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data.rates) {
      throw new Error('Invalid response from OpenExchangeRates');
    }

    const result = {
      success: true,
      base: 'USD',
      rates: data.rates,
      lastSync: new Date().toISOString(),
      source: 'openexchangerates.org',
      timestamp: Date.now()
    };

    console.log(`✅ Fetched ${Object.keys(data.rates).length} rates for USD base`);

    return result;

  } catch (error) {
    console.error(`❌ Error fetching fresh rates:`, error);
    return null;
  }
}

async function cacheRates(baseCurrency, ratesData) {
  try {
    const cacheKey = `${CACHE_KEY_PREFIX}${baseCurrency}`;
    await FINPIN_KV.put(cacheKey, JSON.stringify(ratesData), {
      expirationTtl: CACHE_DURATION
    });

    console.log(`💾 Cached rates for ${baseCurrency}`);
  } catch (error) {
    console.error('❌ Error caching rates:', error);
  }
}

// Convert USD-based rates to any other base currency
function convertRatesFromUSD(usdRates, targetBase) {
  if (targetBase === 'USD') {
    return usdRates;
  }

  const usdToTargetRate = usdRates.rates[targetBase];
  if (!usdToTargetRate) {
    // If target currency not found, return USD rates with warning
    console.warn(`⚠️ Currency ${targetBase} not found in rates, returning USD rates`);
    return usdRates;
  }

  // Convert all rates from USD base to target base
  const convertedRates = {};

  // Add USD rate (inverse of target rate)
  convertedRates['USD'] = 1 / usdToTargetRate;

  // Convert all other rates
  for (const [currency, rate] of Object.entries(usdRates.rates)) {
    if (currency !== targetBase) {
      convertedRates[currency] = rate / usdToTargetRate;
    }
  }

  // Target currency rate is always 1
  convertedRates[targetBase] = 1;

  return {
    success: true,
    base: targetBase,
    rates: convertedRates,
    lastSync: usdRates.lastSync,
    source: usdRates.source,
    timestamp: Date.now(),
    convertedFrom: 'USD'
  };
}

function getFallbackRates(baseCurrency) {
  console.error('❌ No fallback rates available - API must be working');

  // 禁止使用硬编码汇率！返回错误响应，强制使用API数据
  return {
    success: false,
    error: 'No exchange rate data available - API service required',
    base: baseCurrency,
    rates: {},
    lastSync: new Date().toISOString(),
    source: 'error',
    timestamp: Date.now()
  };
}
