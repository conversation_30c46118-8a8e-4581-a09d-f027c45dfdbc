# FinPin Exchange Rate API - Cloudflare Workers

这是FinPin应用的汇率API服务，部署在Cloudflare Workers上，用于高效缓存和提供汇率数据。

## 🎯 功能特性

### 📊 智能缓存机制
- **24小时缓存**: 汇率数据缓存24小时，减少API调用
- **主流货币优先**: 优先缓存USD、EUR、GBP、JPY、CNY等主流货币
- **自动过期**: 缓存自动过期，确保数据新鲜度
- **降级策略**: API失败时自动使用备用汇率

### 🔄 数据同步
- **每日同步**: 每天凌晨2点UTC自动同步最新汇率
- **手动同步**: 支持手动触发同步更新
- **批量处理**: 一次性更新所有主流货币汇率
- **错误恢复**: 单个货币失败不影响其他货币更新

### 🌐 API接口

#### 1. 获取汇率 `/api/rates`
```bash
GET /api/rates?base=USD
```

**响应示例**:
```json
{
  "success": true,
  "base": "USD",
  "rates": {
    "EUR": 0.92,
    "GBP": 0.78,
    "JPY": 149.0,
    "CNY": 7.14
  },
  "lastSync": "2024-01-15T02:00:00.000Z",
  "source": "openexchangerates.org",
  "timestamp": 1705287600000
}
```

#### 2. 手动同步 `/api/sync`
```bash
POST /api/sync
```

**响应示例**:
```json
{
  "success": true,
  "message": "Sync completed",
  "results": [
    {"currency": "USD", "success": true},
    {"currency": "EUR", "success": true}
  ],
  "timestamp": 1705287600000
}
```

#### 3. 服务状态 `/api/status`
```bash
GET /api/status
```

**响应示例**:
```json
{
  "success": true,
  "status": "operational",
  "lastSync": "2024-01-15T02:00:00.000Z",
  "cacheStats": {
    "USD": "cached",
    "EUR": "cached",
    "GBP": "missing"
  },
  "timestamp": 1705287600000
}
```

## 🚀 部署步骤

### 1. 准备工作
```bash
# 安装Wrangler CLI
npm install -g wrangler

# 登录Cloudflare
wrangler auth login
```

### 2. 创建KV命名空间
```bash
# 创建生产环境KV
wrangler kv:namespace create "FINPIN_KV"

# 创建预览环境KV
wrangler kv:namespace create "FINPIN_KV" --preview
```

### 3. 更新配置
编辑 `wrangler.toml` 文件，更新以下内容：
- `id`: 替换为实际的KV命名空间ID
- `preview_id`: 替换为预览环境的KV命名空间ID
- `zone_name`: 替换为你的域名（如果使用自定义域名）

### 4. 部署Worker
```bash
# 部署到生产环境
wrangler deploy

# 部署到预览环境
wrangler deploy --env staging
```

### 5. 设置定时任务
```bash
# 启用定时任务（每日凌晨2点同步）
wrangler cron trigger --cron "0 2 * * *"
```

## 📱 iOS应用集成

### 1. 更新服务端点
在 `CloudflareExchangeRateService.swift` 中更新 `baseURL`：
```swift
private let baseURL = "https://finpin-rates.your-subdomain.workers.dev"
```

### 2. 替换现有服务
在需要使用新汇率服务的地方，替换为：
```swift
@StateObject private var exchangeRateService = CloudflareExchangeRateService.shared
```

## 🔧 配置说明

### OpenExchangeRates API
- **APP ID**: `********************************`
- **月度限额**: 1000次请求
- **文档**: https://docs.openexchangerates.org/reference/api-introduction

### 缓存策略
- **主要缓存**: 24小时有效期
- **备用缓存**: 永久保存最后成功的汇率
- **降级机制**: API失败时使用内置汇率

### 支持货币
主要支持以下24种货币：
- 美元 (USD)、欧元 (EUR)、英镑 (GBP)、日元 (JPY)
- 人民币 (CNY)、加元 (CAD)、澳元 (AUD)、瑞士法郎 (CHF)
- 韩元 (KRW)、新加坡元 (SGD)、港币 (HKD)、印度卢比 (INR)
- 泰铢 (THB)、墨西哥比索 (MXN)、巴西雷亚尔 (BRL)
- 挪威克朗 (NOK)、瑞典克朗 (SEK)、丹麦克朗 (DKK)
- 波兰兹罗提 (PLN)、捷克克朗 (CZK)、俄罗斯卢布 (RUB)
- 土耳其里拉 (TRY)、南非兰特 (ZAR)、新西兰元 (NZD)

## 📊 监控和维护

### 日志查看
```bash
# 查看实时日志
wrangler tail

# 查看特定时间段的日志
wrangler tail --since 2024-01-15T00:00:00Z
```

### 性能监控
- 在Cloudflare Dashboard中查看Worker性能指标
- 监控API调用次数和响应时间
- 检查KV存储使用情况

### 故障排除
1. **API调用失败**: 检查OpenExchangeRates API额度
2. **缓存问题**: 清空KV存储重新缓存
3. **定时任务**: 确认Cron触发器正常工作

## 💰 成本优化

### API调用优化
- 每日只同步一次，大幅减少API调用
- 智能缓存避免重复请求
- 主流货币优先，按需加载其他货币

### Cloudflare费用
- Workers: 免费额度100,000次请求/天
- KV存储: 免费额度1GB存储 + 100,000次读取/天
- 预计月费用: $0（在免费额度内）

## 🔒 安全考虑

### API密钥保护
- OpenExchangeRates APP ID已硬编码（免费计划）
- 生产环境建议使用环境变量存储敏感信息
- 定期轮换API密钥

### 访问控制
- CORS配置允许所有来源（适合移动应用）
- 可根据需要添加域名白名单
- 考虑添加API密钥验证

这个解决方案将大幅减少对OpenExchangeRates的直接调用，
同时提供高性能、低延迟的汇率服务！🚀
