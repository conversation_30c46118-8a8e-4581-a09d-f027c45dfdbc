// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 70;
	objects = {

/* Begin PBXBuildFile section */
		A1000001000000000000001 /* FinPinApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000002000000000000001 /* FinPinApp.swift */; };
		A1000003000000000000001 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000004000000000000001 /* ContentView.swift */; };
		A1000005000000000000001 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1000006000000000000001 /* Assets.xcassets */; };
		A1000007000000000000001 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1000008000000000000001 /* Preview Assets.xcassets */; };
		A1000009000000000000001 /* LegalDocumentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000010000000000000001 /* LegalDocumentView.swift */; };
		A1000034000000000000001 /* TermsOfService.md in Resources */ = {isa = PBXBuildFile; fileRef = A1000035000000000000001 /* TermsOfService.md */; };
		A1000036000000000000001 /* PrivacyPolicy.md in Resources */ = {isa = PBXBuildFile; fileRef = A1000037000000000000001 /* PrivacyPolicy.md */; };
		D1968DD52E3A448A001DF072 /* premium-1.storekit in Resources */ = {isa = PBXBuildFile; fileRef = D1968DD42E3A448A001DF072 /* premium-1.storekit */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A1000002000000000000001 /* FinPinApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FinPinApp.swift; sourceTree = "<group>"; };
		A1000004000000000000001 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1000006000000000000001 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1000008000000000000001 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A1000010000000000000001 /* LegalDocumentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LegalDocumentView.swift; sourceTree = "<group>"; };
		A1000011000000000000001 /* FinPin.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FinPin.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1000012000000000000001 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A1000033000000000000001 /* FinPin.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = FinPin.entitlements; sourceTree = "<group>"; };
		A1000035000000000000001 /* TermsOfService.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = TermsOfService.md; sourceTree = "<group>"; };
		A1000037000000000000001 /* PrivacyPolicy.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = PrivacyPolicy.md; sourceTree = "<group>"; };
		D1968DD42E3A448A001DF072 /* premium-1.storekit */ = {isa = PBXFileReference; lastKnownFileType = text; path = "premium-1.storekit"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		D102D0AD2E3CEFFE00260DB5 /* AppIcon.appiconset */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = AppIcon.appiconset; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		A1000013000000000000001 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A1000014000000000000001 = {
			isa = PBXGroup;
			children = (
				D1968DD42E3A448A001DF072 /* premium-1.storekit */,
				A1000015000000000000001 /* FinPin */,
				A1000016000000000000001 /* Products */,
			);
			sourceTree = "<group>";
		};
		A1000015000000000000001 /* FinPin */ = {
			isa = PBXGroup;
			children = (
				A1000002000000000000001 /* FinPinApp.swift */,
				A1000018000000000000001 /* Views */,
				A1000035000000000000001 /* TermsOfService.md */,
				A1000037000000000000001 /* PrivacyPolicy.md */,
				A1000006000000000000001 /* Assets.xcassets */,
				A1000012000000000000001 /* Info.plist */,
				A1000033000000000000001 /* FinPin.entitlements */,
				A1000022000000000000001 /* Preview Content */,
				D102D0AD2E3CEFFE00260DB5 /* AppIcon.appiconset */,
			);
			path = FinPin;
			sourceTree = "<group>";
		};
		A1000016000000000000001 /* Products */ = {
			isa = PBXGroup;
			children = (
				A1000011000000000000001 /* FinPin.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1000018000000000000001 /* Views */ = {
			isa = PBXGroup;
			children = (
				A1000004000000000000001 /* ContentView.swift */,
				A1000010000000000000001 /* LegalDocumentView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A1000022000000000000001 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A1000008000000000000001 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1000023000000000000001 /* FinPin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1000024000000000000001 /* Build configuration list for PBXNativeTarget "FinPin" */;
			buildPhases = (
				A1000025000000000000001 /* Sources */,
				A1000013000000000000001 /* Frameworks */,
				A1000026000000000000001 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				D102D0AD2E3CEFFE00260DB5 /* AppIcon.appiconset */,
			);
			name = FinPin;
			productName = FinPin;
			productReference = A1000011000000000000001 /* FinPin.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1000027000000000000001 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					A1000023000000000000001 = {
						CreatedOnToolsVersion = 15.4;
					};
				};
			};
			buildConfigurationList = A1000028000000000000001 /* Build configuration list for PBXProject "FinPin" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				"zh-Hans",
			);
			mainGroup = A1000014000000000000001;
			productRefGroup = A1000016000000000000001 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1000023000000000000001 /* FinPin */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A1000026000000000000001 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				A1000007000000000000001 /* Preview Assets.xcassets in Resources */,
				D1968DD52E3A448A001DF072 /* premium-1.storekit in Resources */,
				A1000034000000000000001 /* TermsOfService.md in Resources */,
				A1000036000000000000001 /* PrivacyPolicy.md in Resources */,
				A1000005000000000000001 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A1000025000000000000001 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				A1000003000000000000001 /* ContentView.swift in Sources */,
				A1000009000000000000001 /* LegalDocumentView.swift in Sources */,
				A1000001000000000000001 /* FinPinApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A1000029000000000000001 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = C5ZV969PR3;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A1000030000000000000001 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = C5ZV969PR3;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A1000031000000000000001 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = FinPin/FinPin.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 10;
				DEVELOPMENT_ASSET_PATHS = "\"FinPin/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = FinPin/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = FinPin;
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.finance";
				INFOPLIST_KEY_NSCameraUsageDescription = "FinPin uses your camera to capture payment receipts and transaction screenshots for expense tracking. This helps you maintain accurate financial records by automatically extracting payment information from images.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "FinPin uses your location to automatically tag expenses with the location where they occurred. This helps you track spending patterns by location and provides context for your financial records. Location data is stored locally on your device.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "FinPin needs access to your photo library to select existing payment receipts and transaction screenshots for expense tracking. This allows you to import financial documents you've already captured.";
				INFOPLIST_KEY_NSSiriUsageDescription = "FinPin uses Siri to allow you to quickly add expenses using voice commands. You can say \"Hey Siri, add expense to FinPin\" to record transactions hands-free. All voice processing is handled by Apple and no audio data is stored by FinPin.";
				INFOPLIST_KEY_NSSupportsLiveActivities = NO;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = armv7;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.finpin.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		A1000032000000000000001 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = FinPin/FinPin.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 10;
				DEVELOPMENT_ASSET_PATHS = "\"FinPin/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = FinPin/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = FinPin;
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.finance";
				INFOPLIST_KEY_NSCameraUsageDescription = "FinPin uses your camera to capture payment receipts and transaction screenshots for expense tracking. This helps you maintain accurate financial records by automatically extracting payment information from images.";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "FinPin uses your location to automatically tag expenses with the location where they occurred. This helps you track spending patterns by location and provides context for your financial records. Location data is stored locally on your device.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "FinPin needs access to your photo library to select existing payment receipts and transaction screenshots for expense tracking. This allows you to import financial documents you've already captured.";
				INFOPLIST_KEY_NSSiriUsageDescription = "FinPin uses Siri to allow you to quickly add expenses using voice commands. You can say \"Hey Siri, add expense to FinPin\" to record transactions hands-free. All voice processing is handled by Apple and no audio data is stored by FinPin.";
				INFOPLIST_KEY_NSSupportsLiveActivities = NO;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = armv7;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.finpin.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A1000024000000000000001 /* Build configuration list for PBXNativeTarget "FinPin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1000031000000000000001 /* Debug */,
				A1000032000000000000001 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1000028000000000000001 /* Build configuration list for PBXProject "FinPin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1000029000000000000001 /* Debug */,
				A1000030000000000000001 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A1000027000000000000001 /* Project object */;
}
