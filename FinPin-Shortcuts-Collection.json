{"shortcuts": [{"name": "FinPin截屏记账", "description": "截取收据或账单，自动提取文本并添加到FinPin", "icon": "📸", "steps": [{"action": "Take Screenshot", "description": "截取屏幕内容"}, {"action": "Get Text from Input", "description": "从截图中提取文本"}, {"action": "Open URL", "url": "finpin://add-expense?text={extracted_text}", "description": "将提取的文本发送到FinPin进行解析"}]}, {"name": "FinPin文本记账", "description": "输入或粘贴费用文本，智能解析并添加记录", "icon": "📝", "steps": [{"action": "Ask for Input", "input_type": "Text", "prompt": "请输入费用信息（如：在星巴克花了25美元）", "description": "请求用户输入费用文本"}, {"action": "Open URL", "url": "finpin://add-expense?text={user_input}", "description": "将输入的文本发送到FinPin进行解析"}]}, {"name": "FinPin相册记账", "description": "从相册选择收据照片，提取文本并记账", "icon": "🖼️", "steps": [{"action": "Select Photos", "description": "从相册选择收据照片"}, {"action": "Get Text from Input", "description": "从选中的照片中提取文本"}, {"action": "Open URL", "url": "finpin://add-expense?text={extracted_text}", "description": "将提取的文本发送到FinPin进行解析"}]}, {"name": "FinPin剪贴板记账", "description": "使用剪贴板中的文本进行记账", "icon": "📋", "steps": [{"action": "Get Clipboard", "description": "获取剪贴板内容"}, {"action": "Open URL", "url": "finpin://add-expense?text={clipboard_text}", "description": "将剪贴板文本发送到FinPin进行解析"}]}, {"name": "FinPin快速记账", "description": "快速记录常见费用类型", "icon": "⚡", "steps": [{"action": "<PERSON><PERSON> from Menu", "options": ["咖啡 $5", "午餐 $15", "打车 $10", "停车 $3", "自定义输入"], "description": "选择常见费用或自定义输入"}, {"action": "If (Custom Input)", "condition": "选择了自定义输入", "then": [{"action": "Ask for Input", "input_type": "Text", "prompt": "请输入费用信息"}]}, {"action": "Open URL", "url": "finpin://add-expense?text={selected_or_input_text}", "description": "将选择或输入的文本发送到FinPin"}]}], "siri_phrases": ["Add expense to FinPin", "Record expense in FinPin", "FinPin add expense", "Log expense to FinPin", "FinPin记账", "用FinPin记账", "添加费用到FinPin"], "url_scheme": {"scheme": "finpin", "host": "add-expense", "parameters": {"text": "要解析的费用文本", "source": "来源标识（可选）"}, "examples": ["finpin://add-expense?text=在星巴克花了25美元", "finpin://add-expense?text=Starbucks $25&source=screenshot", "finpin://add-expense?text=McDonald's ¥35 午餐"]}, "supported_formats": ["银行短信：您在XX商户消费XX元", "支付宝通知：您向XX付款XX元", "微信支付：向XX转账XX元", "收据文本：商户名 + 金额 + 日期", "自然语言：在XX花了XX钱", "英文描述：Spent $XX at XX", "混合语言：McDonald's 花费 ¥35"], "parsing_capabilities": {"amount": "自动识别数字和小数点", "currency": "支持 $, £, €, ¥, ₹ 等符号", "merchant": "提取商户名称或描述", "date": "识别日期格式（今天、昨天、具体日期）", "category": "智能分类（餐饮、交通、购物等）", "payment_method": "识别支付方式（信用卡、现金、支付宝等）"}}