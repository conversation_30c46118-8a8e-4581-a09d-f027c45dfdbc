{"name": "finpin-serverless-api", "version": "1.0.0", "description": "FinPin Serverless API powered by Cloudflare Workers with ARK API integration", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "test": "vitest", "type-check": "tsc --noEmit"}, "keywords": ["finpin", "cloudflare-workers", "serverless", "expense-tracking"], "author": "FinPin Team", "license": "MIT", "devDependencies": {"@cloudflare/workers-types": "^4.20240529.0", "@types/node": "^20.12.12", "typescript": "^5.4.5", "vitest": "^1.6.0", "wrangler": "^3.57.1"}, "dependencies": {"hono": "^4.4.2", "@hono/zod-validator": "^0.2.1", "zod": "^3.23.8"}}