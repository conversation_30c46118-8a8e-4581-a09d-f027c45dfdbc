name = "finpin-api"
main = "src/index.ts"
compatibility_date = "2024-05-30"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "finpin-api"
vars = { ENVIRONMENT = "production", API_VERSION = "v1", RATE_LIMIT_PER_MINUTE = "5", REQUEST_TIMEOUT_SECONDS = "30", SIGNATURE_VALIDITY_MINUTES = "5", ARK_MODEL = "doubao-1-5-lite-32k-250115" }
kv_namespaces = [
  { binding = "CACHE", id = "8602d5456ce7488e8fe2f3706ac78711", preview_id = "f8aa70c09f864582818e4e2a0b8e6a96" },
  { binding = "RATE_LIMIT", id = "837f25897318478bb783bf91e0a7f16c", preview_id = "489696e60ae649aca938af4d507ac579" }
]

[env.staging]
name = "finpin-api-staging"

# Environment variables (use wrangler secret for sensitive data)
[vars]
ENVIRONMENT = "development"
API_VERSION = "v1"
RATE_LIMIT_PER_MINUTE = "10"
REQUEST_TIMEOUT_SECONDS = "30"
SIGNATURE_VALIDITY_MINUTES = "5"
ARK_MODEL = "doubao-1-5-lite-32k-250115"

# KV Namespaces for caching and rate limiting
[[kv_namespaces]]
binding = "CACHE"
id = "8602d5456ce7488e8fe2f3706ac78711"
preview_id = "f8aa70c09f864582818e4e2a0b8e6a96"

[[kv_namespaces]]
binding = "RATE_LIMIT"
id = "837f25897318478bb783bf91e0a7f16c"
preview_id = "489696e60ae649aca938af4d507ac579"

# Secrets (set using: wrangler secret put SECRET_NAME)
# ARK_API_KEY
# MASTER_KEY_SEED
# JWT_SECRET
