#!/bin/bash

# FinPin ARK API Test Script
set -e

echo "🧪 Testing ARK API connection..."

# Check if ARK API key is provided
if [ -z "$ARK_API_KEY" ]; then
    echo "❌ ARK_API_KEY environment variable is required"
    echo "Please set it with: export ARK_API_KEY=your-ark-api-key-here"
    exit 1
fi

# Test ARK API directly
echo "📡 Testing direct ARK API call..."

curl -s https://ark.cn-beijing.volces.com/api/v3/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ARK_API_KEY" \
  -d '{
    "model": "doubao-1-5-lite-32k-250115",
    "messages": [
      {"role": "system", "content": "You are a professional financial transaction parser. Extract structured information from payment text and respond with valid JSON only."},
      {"role": "user", "content": "Parse this payment text: Apple Pay payment $25.00 at Starbucks Coffee. Return JSON format."}
    ],
    "max_tokens": 500,
    "temperature": 0.1
  }' | jq '.'

echo ""
echo "✅ ARK API test completed!"
echo ""
echo "💡 If the test was successful, you can now deploy your Cloudflare Workers:"
echo "   ./scripts/deploy.sh production"
