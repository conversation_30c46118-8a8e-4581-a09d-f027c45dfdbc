import Foundation
import AppIntents

// MARK: - Add Expense App Intent (iOS 16+)
@available(iOS 16.0, *)
struct AddExpenseAppIntent: AppIntent {
    static var title: LocalizedStringResource = "Add Expense to FinPin"
    static var description = IntentDescription("Add an expense to FinPin by providing expense details as text")

    // This is crucial for Shortcuts discovery
    static var openAppWhenRun: Bool = false
    
    static var parameterSummary: some ParameterSummary {
        Summary("Add expense: \(\.$expenseText)")
    }
    
    @Parameter(
        title: "Expense Text", 
        description: "Describe your expense (e.g., 'Spent $25 at Starbucks')",
        requestValueDialog: IntentDialog("What expense would you like to add?")
    )
    var expenseText: String
    
    @MainActor
    func perform() async throws -> some IntentResult & ProvidesDialog {
        guard !expenseText.isEmpty else {
            throw AddExpenseError.missingText
        }
        
        // Process the expense using SiriShortcutsManager
        let success = await SiriShortcutsManager.shared.processExpenseFromSiri(text: expenseText)
        
        if success {
            return .result(dialog: "Successfully added expense: \(expenseText)")
        } else {
            throw AddExpenseError.processingFailed
        }
    }
}

// MARK: - Error Handling
@available(iOS 16.0, *)
enum AddExpenseError: Swift.Error, LocalizedError {
    case missingText
    case processingFailed
    
    var errorDescription: String? {
        switch self {
        case .missingText:
            return "Please provide expense details"
        case .processingFailed:
            return "Failed to process expense. Please try again."
        }
    }
}

// MARK: - App Shortcuts Provider
@available(iOS 16.0, *)
struct FinPinShortcuts: AppShortcutsProvider {
    static var appShortcuts: [AppShortcut] {
        [AppShortcut(
            intent: AddExpenseAppIntent(),
            phrases: [
                "Add expense to FinPin",
                "Record expense in FinPin",
                "Log expense to FinPin",
                "FinPin add expense",
                "Add expense with FinPin"
            ],
            shortTitle: "Add Expense",
            systemImageName: "plus.circle"
        )]
    }
}

// MARK: - Legacy Intent Support (iOS 12-15)
@available(iOS 12.0, *)
extension AddExpenseIntentHandler {
    
    // This ensures the legacy intent handler is available for older iOS versions
    static func register() {
        // Registration happens automatically through Info.plist
        print("✅ Legacy AddExpenseIntentHandler registered for iOS 12-15")
    }
}

// MARK: - Intent Donation Helper
@available(iOS 16.0, *)
struct IntentDonationHelper {
    
    static func donateAddExpenseIntent() {
        let intent = AddExpenseAppIntent()
        intent.expenseText = "Sample expense"
        
        // Donate the intent for Siri suggestions
        Task {
            do {
                try await intent.donate()
                print("✅ Successfully donated AddExpenseAppIntent")
            } catch {
                print("❌ Failed to donate AddExpenseAppIntent: \(error)")
            }
        }
    }
}
