<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>INEnums</key>
	<array/>
	<key>INIntentDefinitionModelVersion</key>
	<string>1.2</string>
	<key>INIntentDefinitionNamespace</key>
	<string>88xZPY</string>
	<key>INIntentDefinitionSystemVersion</key>
	<string>20A294</string>
	<key>INIntentDefinitionToolsBuildVersion</key>
	<string>12A6144</string>
	<key>INIntentDefinitionToolsVersion</key>
	<string>12.0</string>
	<key>INIntents</key>
	<array>
		<dict>
			<key>INIntentCategory</key>
			<string>information</string>
			<key>INIntentConfigurable</key>
			<true/>
			<key>INIntentDescription</key>
			<string>Add an expense to FinPin by providing expense details as text</string>
			<key>INIntentDescriptionID</key>
			<string>gpCwrM</string>
			<key>INIntentEligibleForWidgets</key>
			<true/>
			<key>INIntentIneligibleForSuggestions</key>
			<false/>
			<key>INIntentName</key>
			<string>AddExpense</string>
			<key>INIntentParameters</key>
			<array>
				<dict>
					<key>INIntentParameterConfigurable</key>
					<true/>
					<key>INIntentParameterDisplayName</key>
					<string>Expense Text</string>
					<key>INIntentParameterDisplayNameID</key>
					<string>uCx7tU</string>
					<key>INIntentParameterDisplayPriority</key>
					<integer>1</integer>
					<key>INIntentParameterName</key>
					<string>expenseText</string>
					<key>INIntentParameterPromptDialogs</key>
					<array>
						<dict>
							<key>INIntentParameterPromptDialogCustom</key>
							<true/>
							<key>INIntentParameterPromptDialogType</key>
							<string>Configuration</string>
						</dict>
						<dict>
							<key>INIntentParameterPromptDialogCustom</key>
							<true/>
							<key>INIntentParameterPromptDialogType</key>
							<string>Primary</string>
						</dict>
						<dict>
							<key>INIntentParameterPromptDialogCustom</key>
							<true/>
							<key>INIntentParameterPromptDialogType</key>
							<string>DisambiguationIntroduction</string>
						</dict>
						<dict>
							<key>INIntentParameterPromptDialogCustom</key>
							<true/>
							<key>INIntentParameterPromptDialogType</key>
							<string>DisambiguationItem</string>
						</dict>
					</array>
					<key>INIntentParameterSupportsResolution</key>
					<true/>
					<key>INIntentParameterTag</key>
					<integer>1</integer>
					<key>INIntentParameterType</key>
					<string>String</string>
				</dict>
			</array>
			<key>INIntentResponse</key>
			<dict>
				<key>INIntentResponseCodes</key>
				<array>
					<dict>
						<key>INIntentResponseCodeName</key>
						<string>success</string>
						<key>INIntentResponseCodeSuccess</key>
						<true/>
					</dict>
					<dict>
						<key>INIntentResponseCodeName</key>
						<string>failure</string>
					</dict>
				</array>
				<key>INIntentResponseLastParameterTag</key>
				<integer>3</integer>
				<key>INIntentResponseOutput</key>
				<string>message</string>
				<key>INIntentResponseParameters</key>
				<array>
					<dict>
						<key>INIntentResponseParameterDisplayName</key>
						<string>Success</string>
						<key>INIntentResponseParameterDisplayNameID</key>
						<string>MKlqbF</string>
						<key>INIntentResponseParameterName</key>
						<string>success</string>
						<key>INIntentResponseParameterTag</key>
						<integer>2</integer>
						<key>INIntentResponseParameterType</key>
						<string>Boolean</string>
					</dict>
					<dict>
						<key>INIntentResponseParameterDisplayName</key>
						<string>Message</string>
						<key>INIntentResponseParameterDisplayNameID</key>
						<string>TjkqbF</string>
						<key>INIntentResponseParameterName</key>
						<string>message</string>
						<key>INIntentResponseParameterTag</key>
						<integer>3</integer>
						<key>INIntentResponseParameterType</key>
						<string>String</string>
					</dict>
				</array>
			</dict>
			<key>INIntentTitle</key>
			<string>Add Expense to FinPin</string>
			<key>INIntentTitleID</key>
			<string>LdQqbF</string>
			<key>INIntentType</key>
			<string>Custom</string>
			<key>INIntentVerb</key>
			<string>Unknown</string>
		</dict>
	</array>
	<key>INTypes</key>
	<array/>
</dict>
</plist>
