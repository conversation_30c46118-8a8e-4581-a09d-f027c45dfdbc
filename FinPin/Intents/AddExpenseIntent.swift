import Foundation
import Intents

// MARK: - Add Expense Intent
@available(iOS 12.0, *)
class AddExpenseIntent: INIntent {
    
    @NSManaged public var expenseText: String?
    
    override init() {
        super.init()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    override func copy(with zone: NSZone? = nil) -> Any {
        let copy = AddExpenseIntent()
        copy.expenseText = self.expenseText
        return copy
    }
}

// MARK: - Add Expense Intent Response
@available(iOS 12.0, *)
class AddExpenseIntentResponse: INIntentResponse {
    
    @NSManaged public var success: Bool
    @NSManaged public var message: String?
    
    override init() {
        super.init()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    convenience init(code: AddExpenseIntentResponseCode, userActivity: NSUserActivity?) {
        self.init()
        self.code = code.rawValue
        self.userActivity = userActivity
    }
    
    override func copy(with zone: NSZone? = nil) -> Any {
        let copy = AddExpenseIntentResponse()
        copy.success = self.success
        copy.message = self.message
        return copy
    }
}

// MARK: - Response Codes
@available(iOS 12.0, *)
enum AddExpenseIntentResponseCode: Int {
    case unspecified = 0
    case ready = 1
    case continueInApp = 2
    case inProgress = 3
    case success = 4
    case failure = 5
    case failureRequiringAppLaunch = 6
}

// MARK: - Intent Handler
@available(iOS 12.0, *)
class AddExpenseIntentHandler: NSObject, INIntentHandler {
    
    func handle(intent: AddExpenseIntent, completion: @escaping (AddExpenseIntentResponse) -> Void) {
        
        guard let expenseText = intent.expenseText, !expenseText.isEmpty else {
            let response = AddExpenseIntentResponse(code: .failure, userActivity: nil)
            response.message = "Please provide expense details"
            completion(response)
            return
        }
        
        // Process the expense asynchronously
        Task {
            let success = await SiriShortcutsManager.shared.processExpenseFromSiri(text: expenseText)
            
            await MainActor.run {
                if success {
                    let response = AddExpenseIntentResponse(code: .success, userActivity: nil)
                    response.success = true
                    response.message = "Expense added successfully"
                    completion(response)
                } else {
                    let response = AddExpenseIntentResponse(code: .failure, userActivity: nil)
                    response.success = false
                    response.message = "Failed to add expense"
                    completion(response)
                }
            }
        }
    }
    
    func confirm(intent: AddExpenseIntent, completion: @escaping (AddExpenseIntentResponse) -> Void) {
        let response = AddExpenseIntentResponse(code: .ready, userActivity: nil)
        completion(response)
    }
    
    func resolveExpenseText(for intent: AddExpenseIntent, with completion: @escaping (INStringResolutionResult) -> Void) {
        if let expenseText = intent.expenseText, !expenseText.isEmpty {
            completion(INStringResolutionResult.success(with: expenseText))
        } else {
            completion(INStringResolutionResult.needsValue())
        }
    }
}
