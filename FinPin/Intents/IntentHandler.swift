import Intents

@available(iOS 12.0, *)
class IntentHandler: INExtension {
    
    override func handler(for intent: INIntent) -> Any {
        // This method is called to determine which handler object should be used to handle the intent.
        // You can use this method to return different handler objects based on the type of intent.
        
        if intent is AddExpenseIntent {
            return AddExpenseIntentHandler()
        }
        
        // If the intent is not handled by this extension, return nil.
        return AddExpenseIntentHandler()
    }
}