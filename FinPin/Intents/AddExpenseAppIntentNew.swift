import Foundation
import AppIntents

@available(iOS 16.0, *)
struct AddExpenseAppIntent: AppIntent {
    static var title: LocalizedStringResource = "Add Expense to FinPin"
    static var description = IntentDescription("Add an expense to FinPin by providing expense details as text")
    
    static var parameterSummary: some ParameterSummary {
        Summary("Add expense: \($expenseText)")
    }
    
    @Parameter(
        title: "Expense Text", 
        description: "Describe your expense (e.g., 'Spent $25 at Starbucks')",
        requestValueDialog: IntentDialog("What expense would you like to add?")
    )
    var expenseText: String
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        guard !expenseText.isEmpty else {
            return .result(dialog: "Please provide expense details")
        }
        
        // Process the expense using SiriShortcutsManager
        let success = await SiriShortcutsManager.shared.processExpenseFromSiri(text: expenseText)
        
        if success {
            return .result(dialog: "Successfully added expense: \(expenseText)")
        } else {
            return .result(dialog: "Failed to add expense. Please try again.")
        }
    }
}