import Foundation
import CloudKit

// MARK: - CloudKit Debug Helper
class CloudKitDebugHelper {
    static let shared = CloudKitDebugHelper()
    private let container = CKContainer(identifier: "iCloud.com.finpin.app")
    private let database: CKDatabase
    
    private init() {
        self.database = container.privateCloudDatabase
    }
    
    // 测试 CloudKit 基本连接
    func testConnection() async {
        print("🔍 Testing CloudKit connection...")
        
        container.accountStatus { status, error in
            DispatchQueue.main.async {
                switch status {
                case .available:
                    print("✅ CloudKit account available")
                case .noAccount:
                    print("❌ No iCloud account")
                case .restricted:
                    print("❌ iCloud access restricted")
                case .couldNotDetermine:
                    print("❌ Could not determine iCloud status")
                case .temporarilyUnavailable:
                    print("❌ iCloud temporarily unavailable")
                @unknown default:
                    print("❌ Unknown iCloud status")
                }
                
                if let error = error {
                    print("❌ CloudKit error: \(error)")
                }
            }
        }
    }
    
    // 测试记录类型是否存在
    func testRecordType() async {
        print("🔍 Testing ExpenseRecord record type...")
        
        let query = CKQuery(recordType: "ExpenseRecord", predicate: NSPredicate(value: true))
        query.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
        
        do {
            let (results, _) = try await database.records(matching: query, inZoneWith: nil, desiredKeys: ["recordID", "creationDate", "modificationDate"])
            print("✅ ExpenseRecord record type found")
            print("📊 Found \(results.count) existing records")
            
            for (recordID, result) in results {
                print("📝 Record ID: \(recordID.recordName)")
                if case .success(let record) = result {
                    print("   📅 Created: \(record.creationDate ?? Date())")
                    print("   🔄 Modified: \(record.modificationDate ?? Date())")
                    print("   🏷️ Record Type: \(record.recordType)")
                }
            }
            
        } catch {
            print("❌ Error testing record type: \(error)")
            
            if let ckError = error as? CKError {
                print("🔍 CKError code: \(ckError.code.rawValue)")
                print("🔍 CKError message: \(ckError.localizedDescription)")
                
                switch ckError.code {
                case .unknownItem:
                    print("🎯 Record type 'ExpenseRecord' does not exist")
                case .notAuthenticated:
                    print("🔒 User not authenticated")
                case .permissionFailure:
                    print("🚫 Permission denied")
                default:
                    print("❓ Other CloudKit error: \(ckError.code)")
                }
            }
        }
    }
    
    // 获取容器信息
    func getContainerInfo() async {
        print("🔍 Getting CloudKit container info...")
        
        // 检查容器标识符
        print("📦 Container Identifier: \(container.containerIdentifier)")
        
        // 检查数据库类型
        print("🗄️ Database Type: Private Database")
        
        // 检查当前环境
        if let path = Bundle.main.path(forResource: "FinPin", ofType: "entitlements"),
           let entitlements = NSDictionary(contentsOfFile: path) {
            print("🔧 Environment: \(entitlements["com.apple.developer.icloud-container-environment"] ?? "Unknown")")
        }
    }
    
    // 检查所有可能的记录类型名称
    func checkPossibleRecordTypeNames() async {
        print("🔍 Checking possible record type names...")
        
        let possibleNames = [
            "ExpenseRecord",
            "Expense",
            "Record",
            "CD_ExpenseRecord",  // Core Data生成的名称
            "CD_Expense"
        ]
        
        for recordType in possibleNames {
            print("🔍 Testing record type: '\(recordType)'")
            
            let query = CKQuery(recordType: recordType, predicate: NSPredicate(value: false))
            
            do {
                let (results, _) = try await database.records(matching: query, inZoneWith: nil, desiredKeys: ["recordID"])
                print("✅ Record type '\(recordType)' exists (query returned \(results.count) results)")
                
            } catch {
                if let ckError = error as? CKError {
                    switch ckError.code {
                    case .unknownItem:
                        print("❌ Record type '\(recordType)' does not exist")
                    case .notAuthenticated:
                        print("❌ Authentication issue for '\(recordType)'")
                    default:
                        print("❌ Error for '\(recordType)': \(ckError.code)")
                    }
                }
            }
        }
    }
    
    // 创建测试记录
    func createTestRecord() async {
        print("🔍 Creating test ExpenseRecord...")
        
        let record = CKRecord(recordType: "ExpenseRecord")
        record["amount"] = 99.99
        record["currency"] = "USD"
        record["date"] = Date()
        record["merchant"] = "Test Merchant"
        
        do {
            let savedRecord = try await database.save(record)
            print("✅ Test record created successfully")
            print("📝 Record ID: \(savedRecord.recordID.recordName)")
            
        } catch {
            print("❌ Error creating test record: \(error)")
            
            if let ckError = error as? CKError {
                print("🔍 CKError code: \(ckError.code.rawValue)")
                print("🔍 CKError message: \(ckError.localizedDescription)")
            }
        }
    }
    
    // 查看现有记录的完整内容
    func inspectExistingRecords() async {
        print("🔍 Inspecting existing CloudKit records...")
        
        let query = CKQuery(recordType: "ExpenseRecord", predicate: NSPredicate(value: true))
        query.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
        
        do {
            let (results, _) = try await database.records(matching: query, inZoneWith: nil, desiredKeys: nil)
            print("📊 Found \(results.count) records to inspect")
            
            for (recordID, result) in results {
                print("=" * 40)
                print("📝 Record ID: \(recordID.recordName)")
                
                if case .success(let record) = result {
                    print("🏷️ Record Type: \(record.recordType)")
                    print("📅 Created: \(record.creationDate ?? Date())")
                    print("🔄 Modified: \(record.modificationDate ?? Date())")
                    
                    // 显示所有字段
                    for (key, value) in record.allKeys() {
                        print("   🔑 \(key): \(value)")
                    }
                }
                print("=" * 40)
            }
            
        } catch {
            print("❌ Error inspecting records: \(error)")
        }
    }
    
    // 列出所有记录类型
    func listAllRecordTypes() async {
        print("🔍 Listing all available record types...")
        
        // 这是一个简化的测试，实际上 CloudKit 不直接提供列出所有记录类型的 API
        // 我们可以尝试一些常见的记录类型来测试连接
        
        let testTypes = ["ExpenseRecord", "CD_" /* Core Data */, "Items" /* 示例 */]
        
        for recordType in testTypes {
            print("🔍 Testing record type: \(recordType)")
            
            let query = CKQuery(recordType: recordType, predicate: NSPredicate(value: false))
            
            do {
                let (results, _) = try await database.records(matching: query, inZoneWith: nil, desiredKeys: ["recordID"])
                print("✅ Record type '\(recordType)' is accessible")
                print("📊 Query returned \(results.count) results (expected 0 due to false predicate)")
                
            } catch {
                print("❌ Error with record type '\(recordType)': \(error)")
                
                if let ckError = error as? CKError {
                    switch ckError.code {
                    case .unknownItem:
                        print("  📝 Record type '\(recordType)' does not exist")
                    case .notAuthenticated:
                        print("  🔒 Authentication issue")
                    default:
                        print("  ❓ Other error: \(ckError.code)")
                    }
                }
            }
        }
    }
    
    // 运行所有测试
    func runAllTests() async {
        print("🚀 Starting CloudKit debugging tests...")
        print("=" * 50)
        
        await getContainerInfo()
        print("=" * 50)
        
        await testConnection()
        print("=" * 50)
        
        await checkPossibleRecordTypeNames()
        print("=" * 50)
        
        await testRecordType()
        print("=" * 50)
        
        await createTestRecord()
        print("=" * 50)
        
        print("✅ CloudKit debugging tests completed")
    }
}

// MARK: - 使用说明
/*
 如何使用这个调试助手：

 1. 将此文件添加到你的 Xcode 项目中
 2. 在需要调试的地方调用：
    await CloudKitDebugHelper.shared.runAllTests()

 3. 或者单独调用特定测试：
    await CloudKitDebugHelper.shared.testConnection()
    await CloudKitDebugHelper.shared.testRecordType()

 4. 查看控制台输出以了解问题所在
*/

// MARK: - Network Reachability Manager
class NetworkReachabilityManager {
    static let shared = NetworkReachabilityManager()
    private let reachability: SCNetworkReachability?
    private var isReachable = false
    private var isReachableOnEthernetOrWiFi = false
    
    private init?() {
        var zeroAddress = sockaddr_in()
        zeroAddress.sin_len = UInt8(MemoryLayout.size(ofValue: zeroAddress))
        zeroAddress.sin_family = sa_family_t(AF_INET)
        
        guard let reachability = withUnsafePointer(to: &zeroAddress, {
            $0.withMemoryRebound(to: sockaddr.self, capacity: 1) {
                SCNetworkReachabilityCreateWithAddress(nil, $0)
            }
        }) else {
            return nil
        }
        
        self.reachability = reachability
        setupReachability()
    }
    
    private func setupReachability() {
        guard let reachability = reachability else { return }
        
        var context = SCNetworkReachabilityContext(version: 0,
                                                  info: Unmanaged.passUnretained(self).toOpaque(),
                                                  retain: nil,
                                                  release: nil,
                                                  copyDescription: nil)
        
        SCNetworkReachabilitySetCallback(reachability, { (_, flags, info) in
            guard let info = info else { return }
            let manager = Unmanaged<NetworkReachabilityManager>.fromOpaque(info).takeUnretainedValue()
            manager.updateReachability(flags)
        }, &context)
        
        SCNetworkReachabilityScheduleWithRunLoop(reachability, CFRunLoopGetMain(), CFRunLoopMode.commonModes.rawValue)
        
        // Get initial status
        var flags = SCNetworkReachabilityFlags()
        SCNetworkReachabilityGetFlags(reachability, &flags)
        updateReachability(flags)
    }
    
    private func updateReachability(_ flags: SCNetworkReachabilityFlags) {
        let wasReachable = isReachable
        isReachable = flags.contains(.reachable)
        isReachableOnEthernetOrWiFi = flags.contains(.isWWAN) == false
        
        if wasReachable != isReachable {
            print("🌐 Network reachability changed: \(isReachable ? "Available" : "Unavailable")")
        }
    }
    
    deinit {
        if let reachability = reachability {
            SCNetworkReachabilityUnscheduleFromRunLoop(reachability, CFRunLoopGetMain(), CFRunLoopMode.commonModes.rawValue)
        }
    }
}