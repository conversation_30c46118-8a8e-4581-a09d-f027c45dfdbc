//
//  FilteringTests.swift
//  FinPin
//
//  Created by AI Assistant on 2025-07-29.
//

import XCTest
@testable import FinPin

class FilteringTests: XCTestCase {
    var dataManager: DataManager!
    
    override func setUp() {
        super.setUp()
        dataManager = DataManager()
        createTestData()
    }
    
    override func tearDown() {
        dataManager = nil
        super.tearDown()
    }
    
    func createTestData() {
        let now = Date()
        let calendar = Calendar.current
        
        // Create test expenses with different tags and dates
        let testExpenses = [
            // Today's Coffee expenses
            ExpenseRecord(
                amount: 5.99,
                currency: "USD",
                merchant: "Starbucks",
                date: now.addingTimeInterval(-3600), // 1 hour ago
                latitude: 37.7749,
                longitude: -122.4194,
                locationName: "San Francisco, CA",
                tags: ["Coffee", "Food"],
                paymentMethod: "Apple Pay",
                paymentCard: "Monzo",
                notes: "Morning coffee",
                rawText: "Starbucks payment $5.99"
            ),
            ExpenseRecord(
                amount: 15.50,
                currency: "USD",
                merchant: "Blue Bottle Coffee",
                date: now.addingTimeInterval(-7200), // 2 hours ago
                latitude: 37.7849,
                longitude: -122.4094,
                locationName: "San Francisco, CA",
                tags: ["Coffee"],
                paymentMethod: "Credit Card",
                paymentCard: "Chase",
                notes: "Afternoon coffee",
                rawText: "Blue Bottle Coffee $15.50"
            ),
            // Yesterday's expenses
            ExpenseRecord(
                amount: 25.99,
                currency: "USD",
                merchant: "Starbucks",
                date: calendar.date(byAdding: .day, value: -1, to: now)!,
                latitude: 37.7749,
                longitude: -122.4194,
                locationName: "San Francisco, CA",
                tags: ["Coffee", "Food"],
                paymentMethod: "Apple Pay",
                paymentCard: "Monzo",
                notes: "Morning coffee",
                rawText: "Starbucks payment $25.99"
            ),
            ExpenseRecord(
                amount: 12.50,
                currency: "USD",
                merchant: "McDonald's",
                date: calendar.date(byAdding: .day, value: -1, to: now)!,
                latitude: 37.7849,
                longitude: -122.4094,
                locationName: "San Francisco, CA",
                tags: ["Food"],
                paymentMethod: "Apple Pay",
                paymentCard: "Starling",
                notes: "Quick lunch",
                rawText: "McDonald's payment $12.50"
            ),
            // This week's expenses
            ExpenseRecord(
                amount: 8.75,
                currency: "USD",
                merchant: "Peet's Coffee",
                date: calendar.date(byAdding: .day, value: -4, to: now)!,
                latitude: 37.7549,
                longitude: -122.4394,
                locationName: "San Francisco, CA",
                tags: ["Coffee"],
                paymentMethod: "Debit Card",
                paymentCard: "Bank of America",
                notes: "Weekend coffee",
                rawText: "Peet's Coffee $8.75"
            ),
            // This month's expenses
            ExpenseRecord(
                amount: 18.99,
                currency: "USD",
                merchant: "Philz Coffee",
                date: calendar.date(byAdding: .day, value: -15, to: now)!,
                latitude: 37.7649,
                longitude: -122.4194,
                locationName: "San Francisco, CA",
                tags: ["Coffee", "Food"],
                paymentMethod: "Apple Pay",
                paymentCard: "Wells Fargo",
                notes: "Special blend coffee",
                rawText: "Philz Coffee $18.99"
            ),
            // Older expenses (beyond this month)
            ExpenseRecord(
                amount: 120.00,
                currency: "USD",
                merchant: "Amazon",
                date: calendar.date(byAdding: .month, value: -2, to: now)!,
                latitude: 37.7749,
                longitude: -122.4194,
                locationName: "San Francisco, CA",
                tags: ["Shopping", "Electronics"],
                paymentMethod: "Credit Card",
                paymentCard: "Amazon Card",
                notes: "Online purchase",
                rawText: "Amazon $120.00"
            )
        ]
        
        for expense in testExpenses {
            dataManager.expenses.append(expense)
        }
    }
    
    // MARK: - Checkpoint 1: Test #tag and time filter combination
    
    func testCoffeeTagWithTodayFilter() {
        // Test filtering by "Coffee" tag and "Today" time range
        let filteredExpenses = filterExpenses(tag: "Coffee", timeRange: .today)
        
        // Should return 2 coffee expenses from today
        XCTAssertEqual(filteredExpenses.count, 2, "Should have 2 coffee expenses from today")
        
        // Verify all results have Coffee tag
        for expense in filteredExpenses {
            XCTAssertTrue(expense.tags.contains("Coffee"), "All results should have Coffee tag")
        }
        
        // Verify all results are from today
        let calendar = Calendar.current
        let today = Date()
        for expense in filteredExpenses {
            XCTAssertTrue(calendar.isDate(expense.date, inSameDayAs: today), "All results should be from today")
        }
        
        // Verify total amount
        let totalAmount = filteredExpenses.reduce(0) { $0 + $1.amount }
        XCTAssertEqual(totalAmount, Decimal(21.49), "Total should be $21.49 (5.99 + 15.50)")
    }
    
    func testCoffeeTagWithWeekFilter() {
        // Test filtering by "Coffee" tag and "This Week" time range
        let filteredExpenses = filterExpenses(tag: "Coffee", timeRange: .week)
        
        // Should return 4 coffee expenses from this week (including today)
        XCTAssertEqual(filteredExpenses.count, 4, "Should have 4 coffee expenses from this week")
        
        // Verify all results have Coffee tag
        for expense in filteredExpenses {
            XCTAssertTrue(expense.tags.contains("Coffee"), "All results should have Coffee tag")
        }
    }
    
    func testFoodTagWithTodayFilter() {
        // Test filtering by "Food" tag and "Today" time range
        let filteredExpenses = filterExpenses(tag: "Food", timeRange: .today)
        
        // Should return 1 food expense from today (Starbucks with both Coffee and Food tags)
        XCTAssertEqual(filteredExpenses.count, 1, "Should have 1 food expense from today")
        
        // Verify the result
        let expense = filteredExpenses.first!
        XCTAssertEqual(expense.merchant, "Starbucks")
        XCTAssertTrue(expense.tags.contains("Food"))
        XCTAssertEqual(expense.amount, Decimal(5.99))
    }
    
    // Helper method to simulate the filtering logic from ExpenseLogView
    private func filterExpenses(tag: String, timeRange: ExpenseLogView.TimeRange) -> [ExpenseRecord] {
        var expenses = dataManager.expenses
        
        // Apply tag filter
        if !tag.isEmpty {
            expenses = expenses.filter { expense in
                expense.tags.contains(tag)
            }
        }
        
        // Apply time range filter
        let calendar = Calendar.current
        let now = Date()
        
        switch timeRange {
        case .all:
            break
        case .today:
            expenses = expenses.filter { calendar.isDate($0.date, inSameDayAs: now) }
        case .week:
            let weekAgo = calendar.date(byAdding: .day, value: -7, to: now)!
            expenses = expenses.filter { $0.date >= weekAgo }
        case .month:
            let monthAgo = calendar.date(byAdding: .month, value: -1, to: now)!
            expenses = expenses.filter { $0.date >= monthAgo }
        case .year:
            let yearAgo = calendar.date(byAdding: .year, value: -1, to: now)!
            expenses = expenses.filter { $0.date >= yearAgo }
        }
        
        return expenses
    }
}
