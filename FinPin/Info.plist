<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AppIntentsPackage</key>
	<string>FinPin</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.finpin.app.shortcuts</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>finpin</string>
			</array>
		</dict>
	</array>
	<key>INIntentsRestrictedWhileLocked</key>
	<array/>
	<key>INIntentsSupported</key>
	<array>
		<string>AddExpenseIntent</string>
	</array>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>api.finpin.app</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
			<key>exchange-rates.finpin.app</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSNetworkUsageDescription</key>
	<string>FinPin uses network access to fetch current exchange rates for currency conversion. No personal financial data is transmitted over the network.</string>
	<key>NSSupportsAutomaticTermination</key>
	<true/>
	<key>NSSupportsSuddenTermination</key>
	<true/>
	<key>NSUserActivityTypes</key>
	<array>
		<string>app.finpin.v1.addExpenseVoice</string>
		<string>app.finpin.v1.addExpenseText</string>
		<string>AddExpenseIntent</string>
		<string>AddExpenseAppIntent</string>
	</array>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>UILaunchScreen</key>
	<dict/>
</dict>
</plist>
