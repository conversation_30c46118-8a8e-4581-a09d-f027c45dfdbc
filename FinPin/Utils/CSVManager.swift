//
//  CSVManager.swift
//  FinPin
//
//  Created on 2025-07-29.
//

import Foundation
import UniformTypeIdentifiers

class CSVManager {
    
    // MARK: - CSV Export
    
    static func exportExpensesToCSV(_ expenses: [ExpenseRecord]) -> String {
        var csvContent = "ID,Amount,Currency,Merchant,Date,RecordDate,Latitude,Longitude,LocationName,Tags,PaymentMethod,PaymentCard,Notes,RawText\n"
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        dateFormatter.timeZone = TimeZone(secondsFromGMT: 0) // Use UTC to match the transactionDate format
        
        for expense in expenses {
            let row = [
                expense.id.uuidString,
                "\(NSDecimalNumber(decimal: expense.amount).doubleValue)",
                expense.currency,
                escapeCSVField(expense.merchant ?? ""),
                dateFormatter.string(from: expense.transactionDate ?? expense.date),
                dateFormatter.string(from: expense.date),
                "\(expense.latitude)",
                "\(expense.longitude)",
                escapeCSVField(expense.locationName ?? ""),
                escapeCSVField(expense.tags.joined(separator: ";")),
                escapeCSVField(expense.paymentMethod ?? ""),
                escapeCSVField(expense.paymentCard ?? ""),
                escapeCSVField(expense.notes ?? ""),
                escapeCSVField(expense.rawText ?? "")
            ].joined(separator: ",")
            
            csvContent += row + "\n"
        }
        
        return csvContent
    }
    
    // MARK: - CSV Import
    
    static func importExpensesFromCSV(_ csvContent: String) -> [ExpenseRecord] {
        let lines = csvContent.components(separatedBy: .newlines)
        guard lines.count > 1 else { return [] }
        
        var expenses: [ExpenseRecord] = []
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        dateFormatter.timeZone = TimeZone(secondsFromGMT: 0) // Use UTC to match the exported date format
        
        // Skip header row
        for i in 1..<lines.count {
            let line = lines[i].trimmingCharacters(in: .whitespacesAndNewlines)
            if line.isEmpty { continue }
            
            let fields = parseCSVLine(line)
            guard fields.count >= 13 else { continue }
            
            // Parse fields
            let idString = fields[0]
            let amountString = fields[1]
            let currency = fields[2]
            let merchant = fields[3].isEmpty ? nil : fields[3]
            let transactionDateString = fields[4]
            let recordDateString = fields[5]
            let latitudeString = fields[6]
            let longitudeString = fields[7]
            let locationName = fields[8].isEmpty ? nil : fields[8]
            let tagsString = fields[9]
            let paymentMethod = fields[10].isEmpty ? nil : fields[10]
            let paymentCard = fields[11].isEmpty ? nil : fields[11]
            let notes = fields[12].isEmpty ? nil : fields[12]
            let rawText = fields[13].isEmpty ? nil : fields[13]
            
            // Convert to appropriate types
            guard let amount = Decimal(string: amountString),
                  let transactionDate = dateFormatter.date(from: transactionDateString),
                  let recordDate = dateFormatter.date(from: recordDateString),
                  let latitude = Double(latitudeString),
                  let longitude = Double(longitudeString) else {
                continue
            }
            
            let id = UUID(uuidString: idString) ?? UUID()
            let tags = tagsString.isEmpty ? [] : tagsString.components(separatedBy: ";")
            
            let expense = ExpenseRecord(
                id: id,
                amount: amount,
                currency: currency,
                merchant: merchant,
                date: recordDate,
                transactionDate: transactionDate,
                latitude: latitude,
                longitude: longitude,
                locationName: locationName,
                tags: tags,
                paymentMethod: paymentMethod,
                paymentCard: paymentCard,
                notes: notes,
                rawText: rawText
            )
            
            expenses.append(expense)
        }
        
        return expenses
    }
    
    // MARK: - Helper Methods
    
    private static func escapeCSVField(_ field: String) -> String {
        if field.contains(",") || field.contains("\"") || field.contains("\n") {
            return "\"" + field.replacingOccurrences(of: "\"", with: "\"\"") + "\""
        }
        return field
    }
    
    private static func parseCSVLine(_ line: String) -> [String] {
        var fields: [String] = []
        var currentField = ""
        var insideQuotes = false
        var i = line.startIndex
        
        while i < line.endIndex {
            let char = line[i]
            
            if char == "\"" {
                if insideQuotes {
                    // Check if this is an escaped quote
                    let nextIndex = line.index(after: i)
                    if nextIndex < line.endIndex && line[nextIndex] == "\"" {
                        currentField += "\""
                        i = line.index(after: nextIndex)
                        continue
                    } else {
                        insideQuotes = false
                    }
                } else {
                    insideQuotes = true
                }
            } else if char == "," && !insideQuotes {
                fields.append(currentField)
                currentField = ""
            } else {
                currentField += String(char)
            }
            
            i = line.index(after: i)
        }
        
        fields.append(currentField)
        return fields
    }
    
    // MARK: - File Operations
    
    static func saveCSVToDocuments(_ csvContent: String, filename: String) -> URL? {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return nil
        }
        
        let fileURL = documentsDirectory.appendingPathComponent(filename)
        
        do {
            try csvContent.write(to: fileURL, atomically: true, encoding: .utf8)
            return fileURL
        } catch {
            print("Error saving CSV file: \(error)")
            return nil
        }
    }
    
    static func loadCSVFromDocuments(_ filename: String) -> String? {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return nil
        }
        
        let fileURL = documentsDirectory.appendingPathComponent(filename)
        
        do {
            return try String(contentsOf: fileURL, encoding: .utf8)
        } catch {
            print("Error loading CSV file: \(error)")
            return nil
        }
    }
    
    // MARK: - Sample CSV Template
    
    static func generateSampleCSV() -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        
        let sampleData = [
            [
                UUID().uuidString,
                "25.50",
                "USD",
                "Starbucks",
                dateFormatter.string(from: Date()),
                "40.7128",
                "-74.0060",
                "New York, NY",
                "餐饮;咖啡",
                "信用卡",
                "Visa ****1234",
                "Morning coffee",
                "Starbucks\n$25.50\n2025-07-29 11:30\nVisa ****1234"
            ],
            [
                UUID().uuidString,
                "120.00",
                "USD",
                "Amazon",
                dateFormatter.string(from: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()),
                "47.6062",
                "-122.3321",
                "Seattle, WA",
                "购物;电子产品",
                "借记卡",
                "Mastercard ****5678",
                "Online purchase",
                "Amazon.com\n$120.00\nOrder #123456789"
            ]
        ]
        
        var csvContent = "ID,Amount,Currency,Merchant,Date,Latitude,Longitude,LocationName,Tags,PaymentMethod,PaymentCard,Notes,RawText\n"
        
        for data in sampleData {
            let row = data.map { escapeCSVField($0) }.joined(separator: ",")
            csvContent += row + "\n"
        }
        
        return csvContent
    }
}
