//
//  LegalDocumentView.swift
//  FinPin
//
//  Created by FinPin on 2025-01-30.
//

import SwiftUI

// MARK: - Legal Document Type
enum LegalDocumentType: String, CaseIterable {
    case termsOfService = "Terms of Service"
    case privacyPolicy = "Privacy Policy"
    
    var fileName: String {
        switch self {
        case .termsOfService:
            return "TermsOfService"
        case .privacyPolicy:
            return "PrivacyPolicy"
        }
    }
    
    var title: String {
        return self.rawValue
    }
}

// MARK: - Legal Document View
struct LegalDocumentView: View {
    let documentType: LegalDocumentType
    @Environment(\.dismiss) private var dismiss
    @State private var documentContent: String = ""
    @State private var isLoading: Bool = true
    @State private var errorMessage: String?
    
    var body: some View {
        NavigationView {
            Group {
                if isLoading {
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.2)
                        Text("Loading \(documentType.title)...")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                } else if let errorMessage = errorMessage {
                    VStack(spacing: 16) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 48))
                            .foregroundColor(.orange)
                        
                        Text("Unable to Load Document")
                            .font(.title2)
                            .fontWeight(.medium)
                        
                        Text(errorMessage)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                        
                        Button("Try Again") {
                            loadDocument()
                        }
                        .buttonStyle(.bordered)
                    }
                    .padding()
                } else {
                    ScrollView {
                        VStack(alignment: .leading, spacing: 0) {
                            MarkdownText(documentContent)
                                .padding()
                        }
                    }
                }
            }
            .navigationTitle(documentType.title)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("Done") {
                    dismiss()
                }
            )
        }
        .onAppear {
            loadDocument()
        }
    }
    
    private func loadDocument() {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                let content = try await loadDocumentContent()
                await MainActor.run {
                    self.documentContent = content
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = error.localizedDescription
                    self.isLoading = false
                }
            }
        }
    }
    
    private func loadDocumentContent() async throws -> String {
        guard let path = Bundle.main.path(forResource: documentType.fileName, ofType: "md"),
              let content = try? String(contentsOfFile: path) else {
            throw DocumentError.fileNotFound
        }
        return content
    }
}

// MARK: - Document Error
enum DocumentError: LocalizedError {
    case fileNotFound
    
    var errorDescription: String? {
        switch self {
        case .fileNotFound:
            return "The document file could not be found in the app bundle."
        }
    }
}

// MARK: - Markdown Text View
struct MarkdownText: View {
    let content: String
    
    init(_ content: String) {
        self.content = content
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            ForEach(parseMarkdown(content), id: \.id) { element in
                element.view
            }
        }
    }
    
    private func parseMarkdown(_ text: String) -> [MarkdownElement] {
        let lines = text.components(separatedBy: .newlines)
        var elements: [MarkdownElement] = []
        var currentParagraph: [String] = []
        
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)
            
            if trimmedLine.isEmpty {
                // Empty line - end current paragraph if any
                if !currentParagraph.isEmpty {
                    elements.append(.paragraph(currentParagraph.joined(separator: " ")))
                    currentParagraph = []
                }
            } else if trimmedLine.hasPrefix("# ") {
                // H1 Header
                if !currentParagraph.isEmpty {
                    elements.append(.paragraph(currentParagraph.joined(separator: " ")))
                    currentParagraph = []
                }
                elements.append(.header1(String(trimmedLine.dropFirst(2))))
            } else if trimmedLine.hasPrefix("## ") {
                // H2 Header
                if !currentParagraph.isEmpty {
                    elements.append(.paragraph(currentParagraph.joined(separator: " ")))
                    currentParagraph = []
                }
                elements.append(.header2(String(trimmedLine.dropFirst(3))))
            } else if trimmedLine.hasPrefix("### ") {
                // H3 Header
                if !currentParagraph.isEmpty {
                    elements.append(.paragraph(currentParagraph.joined(separator: " ")))
                    currentParagraph = []
                }
                elements.append(.header3(String(trimmedLine.dropFirst(4))))
            } else if trimmedLine.hasPrefix("- ") {
                // Bullet point
                if !currentParagraph.isEmpty {
                    elements.append(.paragraph(currentParagraph.joined(separator: " ")))
                    currentParagraph = []
                }
                elements.append(.bulletPoint(String(trimmedLine.dropFirst(2))))
            } else if trimmedLine.hasPrefix("**") && trimmedLine.hasSuffix("**") {
                // Bold text (simple case)
                if !currentParagraph.isEmpty {
                    elements.append(.paragraph(currentParagraph.joined(separator: " ")))
                    currentParagraph = []
                }
                let boldText = String(trimmedLine.dropFirst(2).dropLast(2))
                elements.append(.bold(boldText))
            } else if trimmedLine.hasPrefix("*") && trimmedLine.hasSuffix("*") {
                // Italic text (simple case)
                if !currentParagraph.isEmpty {
                    elements.append(.paragraph(currentParagraph.joined(separator: " ")))
                    currentParagraph = []
                }
                let italicText = String(trimmedLine.dropFirst(1).dropLast(1))
                elements.append(.italic(italicText))
            } else {
                // Regular text - add to current paragraph
                currentParagraph.append(trimmedLine)
            }
        }
        
        // Add final paragraph if any
        if !currentParagraph.isEmpty {
            elements.append(.paragraph(currentParagraph.joined(separator: " ")))
        }
        
        return elements
    }
}

// MARK: - Markdown Element
struct MarkdownElement {
    let id = UUID()
    let view: AnyView
    
    static func header1(_ text: String) -> MarkdownElement {
        MarkdownElement(view: AnyView(
            Text(text)
                .font(.largeTitle)
                .fontWeight(.bold)
                .padding(.vertical, 8)
        ))
    }
    
    static func header2(_ text: String) -> MarkdownElement {
        MarkdownElement(view: AnyView(
            Text(text)
                .font(.title2)
                .fontWeight(.semibold)
                .padding(.vertical, 6)
        ))
    }
    
    static func header3(_ text: String) -> MarkdownElement {
        MarkdownElement(view: AnyView(
            Text(text)
                .font(.title3)
                .fontWeight(.medium)
                .padding(.vertical, 4)
        ))
    }
    
    static func paragraph(_ text: String) -> MarkdownElement {
        MarkdownElement(view: AnyView(
            Text(text)
                .font(.body)
                .padding(.vertical, 2)
        ))
    }
    
    static func bulletPoint(_ text: String) -> MarkdownElement {
        MarkdownElement(view: AnyView(
            HStack(alignment: .top, spacing: 8) {
                Text("•")
                    .font(.body)
                    .foregroundColor(.secondary)
                Text(text)
                    .font(.body)
                Spacer()
            }
            .padding(.vertical, 1)
        ))
    }
    
    static func bold(_ text: String) -> MarkdownElement {
        MarkdownElement(view: AnyView(
            Text(text)
                .font(.body)
                .fontWeight(.bold)
                .padding(.vertical, 2)
        ))
    }
    
    static func italic(_ text: String) -> MarkdownElement {
        MarkdownElement(view: AnyView(
            Text(text)
                .font(.body)
                .italic()
                .padding(.vertical, 2)
        ))
    }
}

// MARK: - Preview
struct LegalDocumentView_Previews: PreviewProvider {
    static var previews: some View {
        LegalDocumentView(documentType: .termsOfService)
    }
}
