import SwiftUI

// MARK: - LLM Provider Configuration Model
struct LLMProviderConfig: Identifiable, Codable, Equatable {
    let id: String
    var name: String
    var providerType: LLMProviderType
    var apiKey: String
    var apiEndpoint: String
    var modelName: String
    var isActive: Bool
    
    init(id: String = UUID().uuidString, name: String, providerType: LLMProviderType, apiKey: String, apiEndpoint: String, modelName: String, isActive: Bool = false) {
        self.id = id
        self.name = name
        self.providerType = providerType
        self.apiKey = apiKey
        self.apiEndpoint = apiEndpoint
        self.modelName = modelName
        self.isActive = isActive
    }
    
    static func == (lhs: LLMProviderConfig, rhs: LLMProviderConfig) -> Bool {
        return lhs.id == rhs.id
    }
}

enum LLMProviderType: String, CaseIterable, Codable {
    case openAI = "OpenAI"
    case anthropic = "Anthropic"
    
    var displayName: String {
        switch self {
        case .openAI:
            return "OpenAI"
        case .anthropic:
            return "Anthropic"
        }
    }
    
    var defaultEndpoint: String {
        switch self {
        case .openAI:
            return "https://api.openai.com/v1"
        case .anthropic:
            return "https://api.anthropic.com/v1"
        }
    }
    
    var defaultModel: String {
        switch self {
        case .openAI:
            return "gpt-4-turbo"
        case .anthropic:
            return "claude-3-5-sonnet-20240620"
        }
    }
}

// MARK: - Custom LLM Configuration View
struct CustomLLMConfigView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = CustomLLMConfigViewModel()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Mode Toggle
                VStack(alignment: .leading, spacing: 12) {
                    Text("LLM Parsing Mode")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Picker("Mode", selection: $viewModel.isCustomModeEnabled) {
                        Text("Finpin (Default)").tag(false)
                        Text("Custom (Client → LLM Provider)").tag(true)
                    }
                    .pickerStyle(.segmented)
                    
                    if !viewModel.isCustomModeEnabled {
                        Text("Use Finpin's default AI parsing service")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else {
                        Text("Connect to your own LLM provider for enhanced privacy")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
                .padding(.horizontal)
                .padding(.top)
                
                if viewModel.isCustomModeEnabled {
                    // Provider List
                    List {
                        Section("Configured Providers") {
                            if viewModel.providerConfigs.isEmpty {
                                ContentUnavailableView(
                                    "No providers configured",
                                    systemImage: "server.rack",
                                    description: Text("Add a new LLM provider to get started")
                                )
                            } else {
                                ForEach(viewModel.providerConfigs) { provider in
                                    ProviderRowView(
                                        provider: provider,
                                        isSelected: provider.id == viewModel.activeProviderId,
                                        onToggle: { 
                                            viewModel.setActiveProvider(provider.id)
                                        },
                                        onEdit: {
                                            viewModel.editingProvider = provider
                                            viewModel.isShowingEditor = true
                                        }
                                    )
                                }
                                .onDelete(perform: viewModel.deleteProviders)
                            }
                        }
                    }
                    
                    // Add Button
                    Button(action: {
                        viewModel.editingProvider = nil
                        viewModel.isShowingEditor = true
                    }) {
                        Label("Add Provider", systemImage: "plus")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                    }
                    .padding()
                } else {
                    // Info view when using default mode
                    VStack(spacing: 20) {
                        Image(systemName: "sparkles")
                            .font(.system(size: 60))
                            .foregroundColor(.blue)
                        
                        VStack(spacing: 10) {
                            Text("Default Finpin AI Parsing")
                                .font(.title2)
                                .fontWeight(.bold)
                            
                            Text("Finpin's default AI parsing service provides reliable expense parsing with bank-level security. Your data is processed securely and never stored.")
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                        
                        Spacer()
                    }
                    .padding()
                }
            }
            .navigationTitle("LLM Configuration")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $viewModel.isShowingEditor) {
                ProviderEditorView(
                    provider: $viewModel.editingProvider,
                    onSave: { provider in
                        if let existingIndex = viewModel.providerConfigs.firstIndex(where: { $0.id == provider.id }) {
                            // Update existing provider
                            viewModel.providerConfigs[existingIndex] = provider
                        } else {
                            // Add new provider
                            viewModel.providerConfigs.append(provider)
                        }
                        viewModel.saveProviders()
                        viewModel.isShowingEditor = false
                    }
                )
            }
            .onAppear {
                viewModel.loadProviders()
            }
        }
    }
}

// MARK: - Provider Row View
struct ProviderRowView: View {
    let provider: LLMProviderConfig
    let isSelected: Bool
    let onToggle: () -> Void
    let onEdit: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(provider.name)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                    }
                }
                
                Text("\(provider.providerType.displayName) - \(provider.modelName)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text(provider.apiEndpoint)
                    .font(.caption)
                    .foregroundColor(.blue)
                    .lineLimit(1)
            }
            
            Spacer()
            
            Button(action: onEdit) {
                Image(systemName: "pencil")
                    .foregroundColor(.blue)
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            onToggle()
        }
    }
}

// MARK: - Provider Editor View
struct ProviderEditorView: View {
    @Binding var provider: LLMProviderConfig?
    let onSave: (LLMProviderConfig) -> Void
    @Environment(\.dismiss) private var dismiss
    
    @State private var name: String = ""
    @State private var providerType: LLMProviderType = .openAI
    @State private var apiKey: String = ""
    @State private var apiEndpoint: String = ""
    @State private var modelName: String = ""
    
    var isEditMode: Bool {
        provider != nil
    }
    
    var isValid: Bool {
        !name.isEmpty && !apiKey.isEmpty && !apiEndpoint.isEmpty && !modelName.isEmpty
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("Provider Information") {
                    TextField("Provider Name", text: $name)
                    
                    Picker("Provider Type", selection: $providerType) {
                        ForEach(LLMProviderType.allCases, id: \.self) { type in
                            Text(type.displayName).tag(type)
                        }
                    }
                    .pickerStyle(.menu)
                }
                
                Section("API Configuration") {
                    SecureField("API Key", text: $apiKey)
                    
                    TextField("API Endpoint", text: $apiEndpoint)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                        .keyboardType(.URL)
                    
                    TextField("Model Name", text: $modelName)
                }
                
                Section("Preview") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Name: \(name.isEmpty ? "Untitled" : name)")
                            .font(.caption)
                        
                        Text("Type: \(providerType.displayName)")
                            .font(.caption)
                        
                        Text("Endpoint: \(apiEndpoint.isEmpty ? providerType.defaultEndpoint : apiEndpoint)")
                            .font(.caption)
                            .foregroundColor(.blue)
                        
                        Text("Model: \(modelName.isEmpty ? providerType.defaultModel : modelName)")
                            .font(.caption)
                    }
                    .padding(.vertical, 4)
                }
            }
            .navigationTitle(isEditMode ? "Edit Provider" : "Add Provider")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        let newProvider = LLMProviderConfig(
                            id: provider?.id ?? UUID().uuidString,
                            name: name,
                            providerType: providerType,
                            apiKey: apiKey,
                            apiEndpoint: apiEndpoint.isEmpty ? providerType.defaultEndpoint : apiEndpoint,
                            modelName: modelName.isEmpty ? providerType.defaultModel : modelName,
                            isActive: provider?.isActive ?? false
                        )
                        onSave(newProvider)
                    }
                    .disabled(!isValid)
                }
            }
            .onAppear {
                if let provider = provider {
                    name = provider.name
                    providerType = provider.providerType
                    apiKey = provider.apiKey
                    apiEndpoint = provider.apiEndpoint
                    modelName = provider.modelName
                } else {
                    // Set defaults for new provider
                    providerType = .openAI
                    apiEndpoint = LLMProviderType.openAI.defaultEndpoint
                    modelName = LLMProviderType.openAI.defaultModel
                }
            }
        }
    }
}

// MARK: - View Model
@MainActor
class CustomLLMConfigViewModel: ObservableObject {
    @Published var isCustomModeEnabled: Bool = false
    @Published var providerConfigs: [LLMProviderConfig] = []
    @Published var activeProviderId: String? = nil
    @Published var isShowingEditor: Bool = false
    @Published var editingProvider: LLMProviderConfig? = nil
    
    private let userDefaults = UserDefaults.standard
    private let providersKey = "LLMProviderConfigs"
    private let customModeKey = "IsCustomLLMModeEnabled"
    private let activeProviderKey = "ActiveLLMProviderId"
    
    init() {
        loadProviders()
    }
    
    func loadProviders() {
        // Load custom mode setting
        isCustomModeEnabled = userDefaults.bool(forKey: customModeKey)
        
        // Load active provider ID
        activeProviderId = userDefaults.string(forKey: activeProviderKey)
        
        // Load provider configs
        if let data = userDefaults.data(forKey: providersKey),
           let decoded = try? JSONDecoder().decode([LLMProviderConfig].self, from: data) {
            providerConfigs = decoded
        }
        
        // If no active provider but we have providers, set the first one as active
        if activeProviderId == nil, let firstProvider = providerConfigs.first {
            activeProviderId = firstProvider.id
            saveActiveProvider()
        }
    }
    
    func saveProviders() {
        if let encoded = try? JSONEncoder().encode(providerConfigs) {
            userDefaults.set(encoded, forKey: providersKey)
        }
        
        // If we're in custom mode but have no active provider, set the first one
        if isCustomModeEnabled && activeProviderId == nil, let firstProvider = providerConfigs.first {
            activeProviderId = firstProvider.id
            saveActiveProvider()
        }
    }
    
    func saveCustomMode() {
        userDefaults.set(isCustomModeEnabled, forKey: customModeKey)
        
        // Update APIConfig
        APIConfig.setCustomLLMModeEnabled(isCustomModeEnabled)
    }
    
    func saveActiveProvider() {
        userDefaults.set(activeProviderId, forKey: activeProviderKey)
    }
    
    func setActiveProvider(_ providerId: String) {
        activeProviderId = providerId
        saveActiveProvider()
        
        // Update the provider configs to ensure only one is active
        providerConfigs = providerConfigs.map { provider in
            var updatedProvider = provider
            updatedProvider.isActive = (provider.id == providerId)
            return updatedProvider
        }
        saveProviders()
    }
    
    func deleteProviders(offsets: IndexSet) {
        // Check if we're deleting the active provider
        let deletingActiveProvider = offsets.contains { index in
            index < providerConfigs.count && providerConfigs[index].id == activeProviderId
        }
        
        providerConfigs.remove(atOffsets: offsets)
        saveProviders()
        
        // If we deleted the active provider, clear the active provider ID
        if deletingActiveProvider {
            activeProviderId = nil
            saveActiveProvider()
            
            // If there are other providers, set the first one as active
            if let firstProvider = providerConfigs.first {
                setActiveProvider(firstProvider.id)
            }
        }
    }
}

// MARK: - Preview
#Preview {
    CustomLLMConfigView()
}