import SwiftUI

struct PremiumModuleView: View {
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @Binding var showingPurchasePremiumView: Bool
    
    var body: some View {
        VStack(spacing: 16) {
            if subscriptionManager.hasActiveSubscription {
                // Premium user view
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.title2)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Unlimited Expenses Unlocked ✓")
                            .font(.headline)
                            .foregroundColor(.primary)
                        Text("Thank you for supporting FinPin!")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                }
                .padding()
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.green.opacity(0.1))
                )
            } else {
                // Free user view with improved design
                ZStack {
                    // Background gradient
                    RoundedRectangle(cornerRadius: 16)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .blur(radius: 10)
                    
                    // Main content
                    VStack(spacing: 12) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Free Plan")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.primary)
                                
                                Text("\(subscriptionManager.expenseCount) / \(subscriptionManager.expenseLimit) expenses used")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                        }
                        
                        // Progress bar
                        GeometryReader { geometry in
                            ZStack(alignment: .leading) {
                                Rectangle()
                                    .fill(Color.gray.opacity(0.3))
                                    .frame(height: 6)
                                    .cornerRadius(3)
                                
                                Rectangle()
                                    .fill(
                                        LinearGradient(
                                            gradient: Gradient(colors: [Color.blue, Color.purple]),
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                                    .frame(
                                        width: min(
                                            CGFloat(subscriptionManager.expenseCount) / CGFloat(subscriptionManager.expenseLimit) * geometry.size.width,
                                            geometry.size.width
                                        ),
                                        height: 6
                                    )
                                    .cornerRadius(3)
                            }
                        }
                        .frame(height: 6)
                        
                        HStack {
                            Text("Upgrade to Premium")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                            
                            Image(systemName: "arrow.right")
                                .font(.caption)
                                .foregroundColor(.white)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(
                                    LinearGradient(
                                        gradient: Gradient(colors: [Color.blue, Color.purple]),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                        )
                    }
                    .padding()
                }
                .frame(maxWidth: .infinity)
                .onTapGesture {
                    showingPurchasePremiumView = true
                }
            }
        }
        .padding(.horizontal)
    }
}

struct PremiumModuleView_Previews: PreviewProvider {
    static var previews: some View {
        PremiumModuleView(showingPurchasePremiumView: .constant(false))
    }
}