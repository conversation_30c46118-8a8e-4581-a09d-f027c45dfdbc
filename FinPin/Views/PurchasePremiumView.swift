import SwiftUI
import StoreKit

struct PurchasePremiumView: View {
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @Environment(\.dismiss) private var dismiss
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Usage Stats Section - Minimalist Geek Style
                    VStack(spacing: 16) {
                        Text("USAGE STATS")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.secondary)
                            .tracking(2)
                        
                        // Progress display in minimalist style
                        VStack(spacing: 8) {
                            HStack {
                                Text("\(subscriptionManager.expenseCount)")
                                    .font(.largeTitle)
                                    .fontWeight(.bold)
                                    .foregroundColor(.blue)
                                Text("/")
                                    .font(.title)
                                    .foregroundColor(.secondary)
                                Text("\(subscriptionManager.expenseLimit)")
                                    .font(.largeTitle)
                                    .fontWeight(.bold)
                                    .foregroundColor(.secondary)
                                
                                Spacer()
                            }
                            
                            // Minimalist progress bar
                            GeometryReader { geometry in
                                ZStack(alignment: .leading) {
                                    Rectangle()
                                        .fill(Color.gray.opacity(0.2))
                                    
                                    Rectangle()
                                        .fill(Color.blue)
                                        .frame(width: min(
                                            CGFloat(subscriptionManager.expenseCount) / CGFloat(subscriptionManager.expenseLimit) * geometry.size.width,
                                            geometry.size.width
                                        ))
                                }
                            }
                            .frame(height: 4)
                            .cornerRadius(2)
                        }
                        
                        Text("Expenses recorded")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.black.opacity(0.05))
                    )
                    .padding(.horizontal, 20)
                    
                    // Premium Benefits Section
                    VStack(alignment: .leading, spacing: 16) {
                        Text("PREMIUM BENEFITS")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.secondary)
                            .tracking(2)
                        
                        BenefitRow(
                            icon: "infinity",
                            title: "Unlimited Expenses",
                            description: "No limit on recording expenses, go beyond \(subscriptionManager.expenseLimit) limit"
                        )
                    }
                    .padding(.horizontal, 20)
                    
                    // Subscription Options from StoreKit
                    VStack(alignment: .leading, spacing: 16) {
                        Text("SUBSCRIPTION OPTIONS")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.secondary)
                            .tracking(2)
                        
                        if subscriptionManager.isLoading {
                            HStack {
                                Spacer()
                                ProgressView()
                                    .scaleEffect(1.5)
                                Spacer()
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.black.opacity(0.05))
                            )
                        } else if subscriptionManager.products.isEmpty {
                            VStack(spacing: 12) {
                                Image(systemName: "exclamationmark.triangle")
                                    .font(.title)
                                    .foregroundColor(.orange)
                                
                                Text("No subscription options available")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                
                                Text("Please check your internet connection or try again later")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.black.opacity(0.05))
                            )
                        } else {
                            // Display all available products from StoreKit
                            ForEach(subscriptionManager.products, id: \.id) { product in
                                ProductRow(product: product, subscriptionManager: subscriptionManager)
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                    
                    // Current Status Section
                    if subscriptionManager.hasActiveSubscription {
                        VStack(alignment: .leading, spacing: 16) {
                            Text("CURRENT STATUS")
                                .font(.headline)
                                .fontWeight(.bold)
                                .foregroundColor(.secondary)
                                .tracking(2)
                            
                            VStack(spacing: 12) {
                                HStack {
                                    Image(systemName: "checkmark.seal.fill")
                                        .foregroundColor(.green)
                                    
                                    Text("Premium Active")
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                    
                                    Spacer()
                                }
                                
                                if let expiryText = subscriptionManager.subscriptionExpiryText {
                                    HStack {
                                        Text("Expires:")
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        Text(expiryText)
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        
                                        Spacer()
                                    }
                                }
                                
                                Button(action: {
                                    Task {
                                        await subscriptionManager.restorePurchases()
                                    }
                                }) {
                                    Text("Restore Purchases")
                                        .font(.subheadline)
                                        .foregroundColor(.blue)
                                }
                                .disabled(subscriptionManager.isLoading)
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.green.opacity(0.1))
                            )
                        }
                        .padding(.horizontal, 20)
                    }
                    
                    // Terms and Info
                    VStack(spacing: 8) {
                        Text("Subscriptions renew automatically. Cancel anytime in Settings.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                        
                        HStack(spacing: 20) {
                            Button("Terms of Service") {
                                // Show terms of service
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                            
                            Button("Privacy Policy") {
                                // Show privacy policy
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                        .padding(.top, 4)
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                }
            }
            .navigationTitle("Premium")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("Done") {
                    dismiss()
                }
            )
        }
        .onAppear {
            print("🔄 PurchasePremiumView appeared, loading products...")
            Task {
                await subscriptionManager.loadProducts()
                await subscriptionManager.checkSubscriptionStatus()
            }
        }
        
        // Helper method to determine if we should show the Restore Purchases button
        private func shouldShowRestoreButton() -> Bool {
            guard let error = subscriptionManager.purchaseError else { return false }
            
            // Show restore button for timeout or network errors
            let errorLower = error.lowercased()
            return errorLower.contains("timeout") || 
                   errorLower.contains("network") || 
                   errorLower.contains("connection") ||
                   errorLower.contains("restore") ||
                   errorLower.contains("purchase") ||
                   errorLower.contains("product") ||
                   errorLower.contains("subscription")
        }
        .alert("Purchase Error", isPresented: .constant(subscriptionManager.purchaseError != nil)) {
            Button("OK") {
                subscriptionManager.purchaseError = nil
            }
            // Add Restore Purchases button for certain errors
            if shouldShowRestoreButton() {
                Button("Restore Purchases") {
                    Task {
                        await subscriptionManager.restorePurchases()
                    }
                    subscriptionManager.purchaseError = nil
                }
            }
        } message: {
            Text(subscriptionManager.purchaseError ?? "")
        }
    }
}

struct BenefitRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black.opacity(0.05))
        )
    }
}

struct ProductRow: View {
    let product: Product
    @ObservedObject var subscriptionManager: SubscriptionManager
    
    var body: some View {
        Button(action: {
            Task {
                // Set the selected product and initiate purchase
                await subscriptionManager.purchase()
            }
        }) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(product.displayName)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text(product.displayPrice)
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                    
                    if let description = product.description, !description.isEmpty {
                        Text(description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if subscriptionManager.isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.black.opacity(0.05))
            )
        }
        .disabled(subscriptionManager.isLoading)
        .buttonStyle(PlainButtonStyle())
    }
}

struct PurchasePremiumView_Previews: PreviewProvider {
    static var previews: some View {
        PurchasePremiumView()
    }
}