import Foundation

// MARK: - Cloudflare Exchange Rate Service
@MainActor
class CloudflareExchangeRateService: ObservableObject {
    static let shared = CloudflareExchangeRateService()
    
    @Published var rates: [String: Double] = [:]
    @Published var lastUpdated: Date?
    @Published var isLoading = false
    
    // Cloudflare Workers endpoint (configurable)
    private var baseURL: String {
        // Check if using custom endpoint
        let isUsingCustomEndpoint = UserDefaults.standard.bool(forKey: "isUsingCustomEndpoint")
        let customEndpoint = UserDefaults.standard.string(forKey: "customAPIEndpoint") ?? ""

        if isUsingCustomEndpoint && !customEndpoint.isEmpty {
            // Validate URL format
            if let url = URL(string: customEndpoint), url.scheme == "https" {
                return customEndpoint
            } else {
                print("⚠️ Invalid custom endpoint URL: \(customEndpoint), falling back to default")
            }
        }

        // Default endpoint
        return "https://finpin-api.jaffron.workers.dev"
    }
    private let cacheKey = "cloudflare_exchange_rates_cache"
    private let lastUpdateKey = "cloudflare_exchange_rates_last_update"
    
    // Cache duration: 1 hour (since server updates daily)
    private let cacheValidDuration: TimeInterval = 3600
    
    private init() {
        loadCachedRates()
    }
    
    // MARK: - Public API
    func fetchRates(for baseCurrency: String = "USD") async {
        // Check if cache is still valid
        if let lastUpdate = lastUpdated,
           Date().timeIntervalSince(lastUpdate) < cacheValidDuration,
           !rates.isEmpty {
            print("📊 Using cached exchange rates (valid for \(Int(cacheValidDuration - Date().timeIntervalSince(lastUpdate))) more seconds)")
            return
        }
        
        isLoading = true
        defer { isLoading = false }
        
        do {
            let response = try await fetchFromCloudflare(baseCurrency: baseCurrency)
            
            rates = response.rates
            lastUpdated = Date()
            saveCachedRates()
            
            print("📊 Updated exchange rates from Cloudflare: \(response.rates.count) currencies")
            print("📊 Base currency: \(response.base), Last sync: \(response.lastSync)")
            
        } catch {
            print("❌ Failed to fetch from Cloudflare: \(error)")
            
            // Fallback to cached rates or default rates
            if rates.isEmpty {
                loadFallbackRates()
            }
        }
    }
    
    func getRate(from: String, to: String) -> Double? {
        // If same currency, return 1.0
        if from == to { return 1.0 }
        
        // Get rates for both currencies relative to base (USD)
        let fromRate = rates[from] ?? (from == "USD" ? 1.0 : nil)
        let toRate = rates[to] ?? (to == "USD" ? 1.0 : nil)
        
        guard let fromR = fromRate, let toR = toRate, fromR != 0 else {
            return nil
        }
        
        // Calculate cross rate: (1 / fromRate) * toRate
        return toR / fromR
    }
    
    func convert(amount: Double, from: String, to: String) -> Double? {
        guard let rate = getRate(from: from, to: to) else { return nil }
        return amount * rate
    }
    
    // MARK: - Private Methods
    private func fetchFromCloudflare(baseCurrency: String) async throws -> CloudflareRateResponse {
        guard let url = URL(string: "\(baseURL)/rates?base=\(baseCurrency)") else {
            throw ExchangeRateError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("FinPin-iOS/1.0", forHTTPHeaderField: "User-Agent")
        request.timeoutInterval = 10.0
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw ExchangeRateError.invalidResponse
        }
        
        print("📊 Cloudflare API response: \(httpResponse.statusCode)")
        
        guard httpResponse.statusCode == 200 else {
            throw ExchangeRateError.httpError(httpResponse.statusCode)
        }
        
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        return try decoder.decode(CloudflareRateResponse.self, from: data)
    }
    
    private func loadFallbackRates() {
        print("⚠️ No fallback rates available - only using cached or API data")
        // 禁止使用硬编码汇率！只依赖API和缓存数据
        // 如果没有缓存数据，rates将保持为空，确保不会使用过时的硬编码汇率
    }
    
    private func loadCachedRates() {
        if let data = UserDefaults.standard.data(forKey: cacheKey),
           let cachedRates = try? JSONDecoder().decode([String: Double].self, from: data) {
            rates = cachedRates
        }
        
        if let lastUpdate = UserDefaults.standard.object(forKey: lastUpdateKey) as? Date {
            lastUpdated = lastUpdate
        }
    }
    
    private func saveCachedRates() {
        if let data = try? JSONEncoder().encode(rates) {
            UserDefaults.standard.set(data, forKey: cacheKey)
        }
        UserDefaults.standard.set(lastUpdated, forKey: lastUpdateKey)
    }
}

// MARK: - Data Models
struct CloudflareRateResponse: Codable {
    let success: Bool
    let base: String
    let rates: [String: Double]
    let lastSync: String
    let source: String
    let timestamp: Double
    
    enum CodingKeys: String, CodingKey {
        case success, base, rates
        case lastSync = "last_sync"
        case source, timestamp
    }
}

// MARK: - Error Types
enum ExchangeRateError: Error, LocalizedError {
    case invalidURL
    case invalidResponse
    case httpError(Int)
    case decodingError
    case networkError
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid API URL"
        case .invalidResponse:
            return "Invalid response from server"
        case .httpError(let code):
            return "HTTP error: \(code)"
        case .decodingError:
            return "Failed to decode response"
        case .networkError:
            return "Network connection error"
        }
    }
}
