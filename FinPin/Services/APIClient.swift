import Foundation
import CryptoKit
import UIKit

// MARK: - API Configuration
struct APIConfig {
    static let defaultBaseURL = "https://api.finpin.app"
    static let apiVersion = "v1"
    static let requestTimeout: TimeInterval = 30
    static let maxRetries = 3

    // Custom endpoint support
    private static var customBaseURL: String?

    static var baseURL: String {
        return customBaseURL ?? defaultBaseURL
    }

    static var fullBaseURL: String {
        return "\(baseURL)/api/\(apiVersion)"
    }

    static func setCustomBaseURL(_ url: String?) {
        customBaseURL = url
    }

    static func resetToDefault() {
        customBaseURL = nil
    }

    static var isUsingCustomEndpoint: Bool {
        return customBaseURL != nil
    }
}

// MARK: - API Models
struct DeviceRegistrationRequest: Codable {
    let deviceId: String
    let deviceInfo: DeviceInfo
    
    enum CodingKeys: String, CodingKey {
        case deviceId = "device_id"
        case deviceInfo = "device_info"
    }
    
    struct DeviceInfo: Codable {
        let model: String
        let osVersion: String
        let appVersion: String
        let platform: String
        
        enum CodingKeys: String, CodingKey {
            case model
            case osVersion = "os_version"
            case appVersion = "app_version"
            case platform
        }
    }
}

struct DeviceRegistrationResponse: Codable {
    let success: Bool
    let data: RegistrationData?
    let error: String?
    
    struct RegistrationData: Codable {
        let keySeed: String
        let expiresAt: String
        let deviceToken: String
        
        enum CodingKeys: String, CodingKey {
            case keySeed = "key_seed"
            case expiresAt = "expires_at"
            case deviceToken = "device_token"
        }
    }
}

struct ExpenseParseRequest: Codable {
    let text: String
    let context: ParseContext?
    
    struct ParseContext: Codable {
        let location: String?
        let timestamp: String?
        let imageMetadata: ImageMetadata?
        let timezoneOffset: Int? // in hours from UTC
        
        enum CodingKeys: String, CodingKey {
            case location
            case timestamp
            case imageMetadata = "image_metadata"
            case timezoneOffset = "timezone_offset"
        }
        
        struct ImageMetadata: Codable {
            let width: Int
            let height: Int
            let format: String
        }
    }
}

struct ExpenseParseResponse: Codable {
    let success: Bool
    let data: ParsedExpense?
    let error: String?
    
    struct ParsedExpense: Codable {
        let amount: String
        let currency: String
        let merchant: String?
        let paymentMethod: String?
        let paymentCard: String?
        let location: String?
        let timestamp: String?
        let confidence: Double
        let extensions: Extensions?
        
        enum CodingKeys: String, CodingKey {
            case amount
            case currency
            case merchant
            case paymentMethod = "payment_method"
            case paymentCard = "payment_card"
            case location
            case timestamp
            case confidence
            case extensions
        }
        
        struct Extensions: Codable {
            let category: String?
            let tags: [String]?
            let description: String?
        }
    }
}

// MARK: - API Errors
enum APIError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingError(Error)
    case networkError(Error)
    case serverError(String)
    case authenticationError(String)
    case rateLimitExceeded
    case deviceNotRegistered
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid API URL"
        case .noData:
            return "No data received from server"
        case .decodingError(let error):
            return "Failed to decode response: \(error.localizedDescription)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .serverError(let message):
            return "Server error: \(message)"
        case .authenticationError(let message):
            return "Authentication error: \(message)"
        case .rateLimitExceeded:
            return "Rate limit exceeded. Please try again later."
        case .deviceNotRegistered:
            return "Device not registered. Please restart the app."
        }
    }
}

// MARK: - Security Manager
class SecurityManager {
    private let keychain = KeychainManager()
    
    func getDeviceId() -> String {
        if let existingId = keychain.getString(for: "device_id") {
            return existingId
        }
        
        let newId = generateDeviceId()
        keychain.setString(newId, for: "device_id")
        return newId
    }
    
    private func generateDeviceId() -> String {
        let device = UIDevice.current
        let identifierData = "\(device.model)-\(device.systemVersion)-\(Bundle.main.bundleIdentifier ?? "")"
        
        let data = Data(identifierData.utf8)
        let hash = SHA256.hash(data: data)
        return hash.compactMap { String(format: "%02x", $0) }.joined()
    }
    
    func getKeySeed() -> String? {
        return keychain.getString(for: "key_seed")
    }
    
    func setKeySeed(_ keySeed: String) {
        keychain.setString(keySeed, for: "key_seed")
    }
    
    func generateSignature(deviceId: String, timestamp: String, requestBody: String) throws -> String {
        guard let keySeed = getKeySeed() else {
            throw APIError.deviceNotRegistered
        }
        
        let requestBodyHash = SHA256.hash(data: Data(requestBody.utf8))
        let requestBodyHashString = requestBodyHash.compactMap { String(format: "%02x", $0) }.joined()
        
        let message = "\(timestamp)\(deviceId)\(requestBodyHashString)"
        let messageData = Data(message.utf8)
        let keyData = Data(keySeed.utf8)
        
        let signature = HMAC<SHA256>.authenticationCode(for: messageData, using: SymmetricKey(data: keyData))
        return Data(signature).base64EncodedString()
    }
}

// MARK: - Keychain Manager
class KeychainManager {
    func setString(_ value: String, for key: String) {
        let data = Data(value.utf8)

        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecValueData as String: data
        ]

        SecItemDelete(query as CFDictionary)
        SecItemAdd(query as CFDictionary, nil)
    }

    func getString(for key: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]

        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)

        guard status == errSecSuccess,
              let data = result as? Data,
              let string = String(data: data, encoding: .utf8) else {
            return nil
        }

        return string
    }
}

// MARK: - API Client
@MainActor
class APIClient: ObservableObject {
    static let shared = APIClient()

    private let security = SecurityManager()
    private let session: URLSession

    @Published var isRegistered = false
    @Published var isLoading = false

    private init() {
        // Load custom endpoint settings from UserDefaults
        let isUsingCustomEndpoint = UserDefaults.standard.bool(forKey: "isUsingCustomEndpoint")
        let customEndpoint = UserDefaults.standard.string(forKey: "customAPIEndpoint") ?? ""

        if isUsingCustomEndpoint && !customEndpoint.isEmpty {
            // Validate URL format
            if let url = URL(string: customEndpoint), url.scheme == "https" {
                let cleanEndpoint = customEndpoint.hasSuffix("/") ? String(customEndpoint.dropLast()) : customEndpoint
                APIConfig.setCustomBaseURL(cleanEndpoint)
                print("APIClient: Using custom endpoint: \(cleanEndpoint)")
            } else {
                print("APIClient: Invalid custom endpoint, using default")
                APIConfig.resetToDefault()
            }
        } else {
            APIConfig.resetToDefault()
        }

        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = APIConfig.requestTimeout
        config.timeoutIntervalForResource = APIConfig.requestTimeout * 2
        self.session = URLSession(configuration: config)

        // Check if device is already registered
        self.isRegistered = security.getKeySeed() != nil
    }

    // MARK: - Device Registration
    func registerDevice() async throws {
        isLoading = true
        defer { isLoading = false }

        let deviceId = security.getDeviceId()
        let device = UIDevice.current

        let request = DeviceRegistrationRequest(
            deviceId: deviceId,
            deviceInfo: DeviceRegistrationRequest.DeviceInfo(
                model: device.model,
                osVersion: device.systemVersion,
                appVersion: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
                platform: "ios"
            )
        )

        let response: DeviceRegistrationResponse = try await performRequest(
            endpoint: "/device/register",
            method: "POST",
            body: request,
            requiresAuth: false
        )

        guard response.success, let data = response.data else {
            throw APIError.serverError(response.error ?? "Registration failed")
        }

        security.setKeySeed(data.keySeed)
        isRegistered = true
    }

    // MARK: - Expense Parsing
    func parseExpense(text: String, context: ExpenseParseRequest.ParseContext? = nil) async throws -> ExpenseParseResponse.ParsedExpense {
        if !isRegistered {
            try await registerDevice()
        }

        isLoading = true
        defer { isLoading = false }

        var parseContext = context ?? ParseContext(location: nil, timestamp: nil, imageMetadata: nil, timezoneOffset: nil)
        let timezoneOffset = TimeZone.current.secondsFromGMT() / 3600
        parseContext = ParseContext(location: parseContext.location, timestamp: parseContext.timestamp, imageMetadata: parseContext.imageMetadata, timezoneOffset: timezoneOffset)
        let request = ExpenseParseRequest(text: text, context: parseContext)

        let response: ExpenseParseResponse = try await performRequest(
            endpoint: "/parse/expense",
            method: "POST",
            body: request,
            requiresAuth: true
        )

        guard response.success, let data = response.data else {
            throw APIError.serverError(response.error ?? "Parsing failed")
        }
print("DEBUG: Parsed timestamp: \(data.timestamp ?? \"nil\")\")

        return data
    }

    // MARK: - Custom Endpoint Management
    func setCustomEndpoint(_ endpoint: String) {
        // Validate URL format
        guard let url = URL(string: endpoint),
              url.scheme == "https" else {
            print("Warning: Invalid custom endpoint URL: \(endpoint)")
            return
        }

        // Remove trailing slash if present
        let cleanEndpoint = endpoint.hasSuffix("/") ? String(endpoint.dropLast()) : endpoint
        APIConfig.setCustomBaseURL(cleanEndpoint)

        // Reset registration status when changing endpoints
        isRegistered = false

        print("Custom API endpoint set to: \(cleanEndpoint)")
    }

    func resetToDefaultEndpoint() {
        APIConfig.resetToDefault()

        // Reset registration status when changing endpoints
        isRegistered = false

        print("API endpoint reset to default: \(APIConfig.defaultBaseURL)")
    }

    var currentEndpoint: String {
        return APIConfig.baseURL
    }

    var isUsingCustomEndpoint: Bool {
        return APIConfig.isUsingCustomEndpoint
    }

    // MARK: - Generic Request Method
    private func performRequest<T: Codable, R: Codable>(
        endpoint: String,
        method: String,
        body: T? = nil,
        requiresAuth: Bool = false
    ) async throws -> R {

        guard let url = URL(string: "\(APIConfig.fullBaseURL)\(endpoint)") else {
            throw APIError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = method
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // Add request body if provided
        var requestBodyString = ""
        if let body = body {
            let bodyData = try JSONEncoder().encode(body)
            request.httpBody = bodyData
            requestBodyString = String(data: bodyData, encoding: .utf8) ?? ""
        }

        // Add authentication headers if required
        if requiresAuth {
            let deviceId = security.getDeviceId()
            let timestamp = String(Int(Date().timeIntervalSince1970 * 1000))

            let signature = try security.generateSignature(
                deviceId: deviceId,
                timestamp: timestamp,
                requestBody: requestBodyString
            )

            request.setValue(deviceId, forHTTPHeaderField: "x-device-id")
            request.setValue(timestamp, forHTTPHeaderField: "x-timestamp")
            request.setValue(signature, forHTTPHeaderField: "x-signature")
        }

        // Perform request with retry logic
        var lastError: Error?

        for attempt in 1...APIConfig.maxRetries {
            do {
                let (data, response) = try await session.data(for: request)

                guard let httpResponse = response as? HTTPURLResponse else {
                    throw APIError.networkError(URLError(.badServerResponse))
                }

                // Handle HTTP status codes
                switch httpResponse.statusCode {
                case 200...299:
                    // Print server response for debugging
                    if let responseString = String(data: data, encoding: .utf8) {
                        print("🔍 SERVER RESPONSE JSON:")
                        print(responseString)
                        print("🔍 END SERVER RESPONSE")
                    }
                    break
                case 401:
                    // Authentication error - try to re-register
                    if requiresAuth && attempt == 1 {
                        try await registerDevice()
                        continue
                    }
                    throw APIError.authenticationError("Authentication failed")
                case 429:
                    throw APIError.rateLimitExceeded
                default:
                    let errorMessage = String(data: data, encoding: .utf8) ?? "Unknown error"
                    throw APIError.serverError("HTTP \(httpResponse.statusCode): \(errorMessage)")
                }

                // Decode response
                do {
                    let decoder = JSONDecoder()
                    return try decoder.decode(R.self, from: data)
                } catch {
                    throw APIError.decodingError(error)
                }

            } catch {
                lastError = error

                // Don't retry on certain errors
                if case APIError.authenticationError = error,
                   case APIError.rateLimitExceeded = error {
                    throw error
                }

                // Wait before retry (exponential backoff)
                if attempt < APIConfig.maxRetries {
                    let delay = pow(2.0, Double(attempt - 1))
                    try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                }
            }
        }

        throw lastError ?? APIError.networkError(URLError(.unknown))
    }
}
