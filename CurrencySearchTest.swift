import Foundation

// 测试货币搜索功能
struct CurrencySearchTest {
    
    // 模拟 CurrencyHelper 的搜索逻辑
    static func testCurrencySearch() {
        print("🧪 开始测试货币搜索功能...")
        
        let majorCurrencies = ["USD", "EUR", "GBP", "JPY", "CNY", "CAD", "AUD", "CHF", "KRW", "SGD"]
        let allCurrencies = [
            "AED", "AFN", "ALL", "AMD", "ANG", "AOA", "ARS", "AUD", "AWG", "AZN",
            "BAM", "BBD", "BDT", "BGN", "BHD", "BIF", "BMD", "BND", "BOB", "BRL",
            "BSD", "BTC", "BTN", "BWP", "BYN", "BZD", "CAD", "CDF", "CHF", "CLF",
            "CLP", "CNH", "CNY", "COP", "CRC", "CUC", "CUP", "CVE", "CZK", "DJF",
            "DKK", "DOP", "DZD", "EGP", "ERN", "ETB", "EUR", "FJD", "FKP", "GBP",
            "GEL", "GGP", "GHS", "GIP", "GMD", "GNF", "GTQ", "GYD", "HKD", "HNL",
            "HRK", "HTG", "HUF", "IDR", "ILS", "IMP", "INR", "IQD", "IRR", "ISK",
            "JEP", "JMD", "JOD", "JPY", "KES", "KGS", "KHR", "KMF", "KPW", "KRW",
            "KWD", "KYD", "KZT", "LAK", "LBP", "LKR", "LRD", "LSL", "LYD", "MAD",
            "MDL", "MGA", "MKD", "MMK", "MNT", "MOP", "MRU", "MUR", "MVR", "MWK",
            "MXN", "MYR", "MZN", "NAD", "NGN", "NIO", "NOK", "NPR", "NZD", "OMR",
            "PAB", "PEN", "PGK", "PHP", "PKR", "PLN", "PYG", "QAR", "RON", "RSD",
            "RUB", "RWF", "SAR", "SBD", "SCR", "SDG", "SEK", "SGD", "SHP", "SLE",
            "SLL", "SOS", "SRD", "SSP", "STD", "STN", "SVC", "SYP", "SZL", "THB",
            "TJS", "TMT", "TND", "TOP", "TRY", "TTD", "TWD", "TZS", "UAH", "UGX",
            "USD", "UYU", "UZS", "VES", "VND", "VUV", "WST", "XAF", "XAG", "XAU",
            "XCD", "XCG", "XDR", "XOF", "XPD", "XPF", "XPT", "YER", "ZAR", "ZMW",
            "ZWG", "ZWL"
        ]
        
        let currencyNames: [String: String] = [
            "THB": "泰铢", "INR": "印度卢比", "BRL": "巴西雷亚尔", "RUB": "俄罗斯卢布",
            "HKD": "港币", "TWD": "新台币", "MYR": "马来西亚林吉特", "PHP": "菲律宾比索",
            "IDR": "印尼盾", "VND": "越南盾", "BTC": "比特币", "XAU": "黄金"
        ]
        
        // 测试搜索功能
        func searchCurrencies(searchText: String) -> [String] {
            if searchText.isEmpty {
                return []
            }
            
            let searchUpper = searchText.uppercased()
            return allCurrencies.filter { currency in
                !majorCurrencies.contains(currency) &&
                (currency.uppercased().contains(searchUpper) ||
                 (currencyNames[currency] ?? currency).uppercased().contains(searchUpper))
            }.sorted()
        }
        
        // 测试用例
        let testCases = [
            ("THB", "搜索泰铢代码"),
            ("泰铢", "搜索泰铢中文名"),
            ("BTC", "搜索比特币"),
            ("印度", "搜索印度相关货币"),
            ("RU", "搜索包含RU的货币"),
            ("港", "搜索港币"),
            ("XA", "搜索贵金属货币")
        ]
        
        for (searchTerm, description) in testCases {
            let results = searchCurrencies(searchText: searchTerm)
            print("\n📝 \(description) (\(searchTerm)):")
            print("   结果数量: \(results.count)")
            print("   前5个结果: \(Array(results.prefix(5)))")
        }
        
        // 验证总数
        print("\n📊 统计信息:")
        print("   主流货币数量: \(majorCurrencies.count)")
        print("   总支持货币数量: \(allCurrencies.count)")
        print("   可搜索货币数量: \(allCurrencies.count - Set(majorCurrencies).intersection(Set(allCurrencies)).count)")
        
        print("\n✅ 货币搜索功能测试完成!")
    }
}

// 运行测试
CurrencySearchTest.testCurrencySearch()