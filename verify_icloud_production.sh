#!/bin/bash

# iCloud Production Environment Verification Script
# This script helps verify that iCloud environment is properly configured for production

echo "🔍 Verifying iCloud Production Configuration..."
echo "=============================================="

# Check entitlements file
ENTITLEMENTS_FILE="FinPin/FinPin.entitlements"
if [ -f "$ENTITLEMENTS_FILE" ]; then
    echo "✅ Found entitlements file: $ENTITLEMENTS_FILE"
    
    # Check iCloud container environment
    ENV=$(grep -A 1 "com.apple.developer.icloud-container-environment" "$ENTITLEMENTS_FILE" | grep "<string>" | sed 's/.*<string>\(.*\)<\/string>.*/\1/')
    echo "🔧 iCloud Container Environment: $ENV"
    
    if [ "$ENV" = "Production" ]; then
        echo "✅ Production environment configured correctly"
    else
        echo "❌ Environment not set to Production: $ENV"
        exit 1
    fi
    
    # Check APNS environment
    APNS_ENV=$(grep -A 1 "aps-environment" "$ENTITLEMENTS_FILE" | grep "<string>" | sed 's/.*<string>\(.*\)<\/string>.*/\1/')
    echo "🔧 APNS Environment: $APNS_ENV"
    
    if [ "$APNS_ENV" = "production" ]; then
        echo "✅ Production APNS configured correctly"
    else
        echo "❌ APNS not set to Production: $APNS_ENV"
        exit 1
    fi
    
    # Check iCloud container identifier
    CONTAINER=$(grep -A 1 "iCloud.com.finpin.app" "$ENTITLEMENTS_FILE" | grep "<string>" | sed 's/.*<string>\(.*\)<\/string>.*/\1/')
    echo "🔧 iCloud Container: $CONTAINER"
    
else
    echo "❌ Entitlements file not found: $ENTITLEMENTS_FILE"
    exit 1
fi

echo ""
echo "📱 Checking CloudKit Configuration in Source Code..."
echo "================================================="

# Check CloudKit container in source files
CK_CONTAINER=$(grep -r "iCloud.com.finpin.app" FinPin/ | head -1)
if [ -n "$CK_CONTAINER" ]; then
    echo "✅ CloudKit container found in source code"
    echo "   $CK_CONTAINER"
else
    echo "❌ CloudKit container not found in source code"
    exit 1
fi

echo ""
echo "🚀 Production Environment Setup Complete!"
echo "========================================="
echo ""
echo "Next steps for App Store submission:"
echo "1. ✅ iCloud container environment set to Production"
echo "2. ✅ APNS environment set to Production"  
echo "3. ✅ CloudKit container identifier: iCloud.com.finpin.app"
echo "4. ⚠️  Make sure CloudKit schema is deployed to Production"
echo "5. ⚠️  Test iCloud sync functionality with Production environment"
echo "6. ⚠️  Verify App Store Connect CloudKit configuration"
echo ""
echo "To verify CloudKit schema deployment:"
echo "   - Open Xcode project"
echo "   - Go to CloudKit Dashboard"
echo "   - Select 'Production' environment"
echo "   - Verify 'ExpenseRecord' record type exists"
echo ""
echo "To test Production sync:"
echo "   - Build app in Release configuration"
echo "   - Test iCloud sync functionality"
echo "   - Check console logs for CloudKit operations"