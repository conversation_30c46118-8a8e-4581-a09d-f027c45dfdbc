{"WFWorkflowMinimumClientVersionString": "900", "WFWorkflowMinimumClientVersion": 900, "WFWorkflowIcon": {"WFWorkflowIconStartColor": 2071128575, "WFWorkflowIconGlyphNumber": 61440}, "WFWorkflowClientVersion": "2605.0.5", "WFWorkflowOutputContentItemClasses": [], "WFWorkflowHasOutputFallback": false, "WFWorkflowActions": [{"WFWorkflowActionIdentifier": "is.workflow.actions.screenshottool", "WFWorkflowActionParameters": {}}, {"WFWorkflowActionIdentifier": "is.workflow.actions.detect.text", "WFWorkflowActionParameters": {}}, {"WFWorkflowActionIdentifier": "is.workflow.actions.gettext", "WFWorkflowActionParameters": {}}, {"WFWorkflowActionIdentifier": "is.workflow.actions.openurl", "WFWorkflowActionParameters": {"WFInput": {"Value": {"string": "finpin://add-expense?text=", "attachmentsByRange": {"{26, 1}": {"Type": "ActionOutput", "OutputUUID": "previous-action-uuid", "OutputName": "Text"}}}, "WFSerializationType": "WFTextTokenString"}}}], "WFWorkflowInputContentItemClasses": ["WFAppStoreAppContentItem", "WFArticleContentItem", "WFContactContentItem", "WFDateContentItem", "WFEmailAddressContentItem", "WFGenericFileContentItem", "WFImageContentItem", "WFiTunesProductContentItem", "WFLocationContentItem", "WFDCMapsLinkContentItem", "WFAVAssetContentItem", "WFPDFContentItem", "WFPhoneNumberContentItem", "WFRichTextContentItem", "WFSafariWebPageContentItem", "WFStringContentItem", "WFURLContentItem"], "WFWorkflowImportQuestions": [], "WFWorkflowTypes": [], "WFQuickActionSurfaces": [], "WFWorkflowHasShortcutInputVariables": false}